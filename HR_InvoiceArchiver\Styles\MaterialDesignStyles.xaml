<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Material Design Color Palette -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#1976D2"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#42A5F5"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#0D47A1"/>
    <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="#FFEB3B"/>
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="#FF8F00"/>
    
    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
    <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>
    
    <SolidColorBrush x:Key="OnSurfaceBrush" Color="#212121"/>
    <SolidColorBrush x:Key="OnBackgroundBrush" Color="#212121"/>
    <SolidColorBrush x:Key="OnPrimaryBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="OnSecondaryBrush" Color="#000000"/>
    
    <!-- Shadow Effects -->
    <DropShadowEffect x:Key="ElevationShadow1" Color="#40000000" Direction="270" ShadowDepth="1" BlurRadius="3"/>
    <DropShadowEffect x:Key="ElevationShadow2" Color="#40000000" Direction="270" ShadowDepth="2" BlurRadius="6"/>
    <DropShadowEffect x:Key="ElevationShadow3" Color="#40000000" Direction="270" ShadowDepth="4" BlurRadius="8"/>
    <DropShadowEffect x:Key="ElevationShadow4" Color="#40000000" Direction="270" ShadowDepth="6" BlurRadius="12"/>

    <!-- Modern Primary Button Style -->
    <Style x:Key="ModernPrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#007BFF" Offset="0"/>
                    <GradientStop Color="#0056B3" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        <Border.RenderTransform>
                            <ScaleTransform/>
                        </Border.RenderTransform>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Secondary Button Style -->
    <Style x:Key="ModernSecondaryButtonStyle" TargetType="Button">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#28A745" Offset="0"/>
                    <GradientStop Color="#1E7E34" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="#28A745" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        <Border.RenderTransform>
                            <ScaleTransform/>
                        </Border.RenderTransform>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Special Button Style -->
    <Style x:Key="ModernSpecialButtonStyle" TargetType="Button">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#6F42C1" Offset="0"/>
                    <GradientStop Color="#5A2D91" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="#6F42C1" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        <Border.RenderTransform>
                            <ScaleTransform/>
                        </Border.RenderTransform>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Card Style -->
    <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Background" Value="White"/>
    </Style>

    <!-- Stat Card Style -->
    <Style x:Key="StatCardStyle" TargetType="materialDesign:Card">
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>

    <!-- Material Design Button Styles -->
    <Style x:Key="MaterialPrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource OnPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="4"/>
        <Setter Property="materialDesign:RippleAssist.Feedback" Value="{StaticResource OnPrimaryBrush}"/>
    </Style>

    <Style x:Key="MaterialSecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="4"/>
    </Style>

    <Style x:Key="MaterialTextButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="36"/>
    </Style>

    <!-- Material Design Input Styles -->
    <Style x:Key="MaterialTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.HasOutlinedTextField" Value="True"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <Style x:Key="MaterialComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <Style x:Key="MaterialDatePicker" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <!-- Material Design DataGrid Style -->
    <Style x:Key="MaterialDataGrid" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="RowHeight" Value="48"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <!-- Navigation Rail Style -->
    <Style x:Key="NavigationRailStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="BorderThickness" Value="0,0,1,0"/>
        <Setter Property="Width" Value="80"/>
        <Setter Property="Effect" Value="{StaticResource ElevationShadow1}"/>
    </Style>

    <!-- Navigation Item Style -->
    <Style x:Key="NavigationItemStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="Width" Value="64"/>
        <Setter Property="Height" Value="64"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="16"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Style.Triggers>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource OnPrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- App Bar Style -->
    <Style x:Key="AppBarStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Height" Value="64"/>
        <Setter Property="Effect" Value="{StaticResource ElevationShadow2}"/>
    </Style>

    <!-- Typography Styles -->
    <Style x:Key="HeadlineStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource OnBackgroundBrush}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <Style x:Key="TitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource OnBackgroundBrush}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <Style x:Key="SubtitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource OnBackgroundBrush}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <Style x:Key="BodyStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{StaticResource OnBackgroundBrush}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <Style x:Key="CaptionStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="#757575"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

</ResourceDictionary>
