<UserControl x:Class="HR_InvoiceArchiver.Controls.LoadingSkeleton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <UserControl.Resources>
        <!-- Shimmer Animation -->
        <Storyboard x:Key="ShimmerAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="ShimmerTransform"
                           Storyboard.TargetProperty="X"
                           From="-100" To="400"
                           Duration="0:0:1.5"
                           AutoReverse="False"/>
        </Storyboard>
        
        <!-- Skeleton Style -->
        <Style x:Key="SkeletonRectangle" TargetType="Rectangle">
            <Setter Property="Fill">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#F0F0F0" Offset="0"/>
                        <GradientStop Color="#E0E0E0" Offset="0.5"/>
                        <GradientStop Color="#F0F0F0" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="RadiusX" Value="4"/>
            <Setter Property="RadiusY" Value="4"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <!-- Shimmer Overlay -->
        <Rectangle x:Name="ShimmerOverlay" Opacity="0.3">
            <Rectangle.Fill>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <LinearGradientBrush.Transform>
                        <TranslateTransform x:Name="ShimmerTransform" X="-100"/>
                    </LinearGradientBrush.Transform>
                    <GradientStop Color="Transparent" Offset="0"/>
                    <GradientStop Color="White" Offset="0.5"/>
                    <GradientStop Color="Transparent" Offset="1"/>
                </LinearGradientBrush>
            </Rectangle.Fill>
        </Rectangle>

        <!-- Skeleton Content -->
        <StackPanel x:Name="SkeletonContent" Margin="20">
            <!-- Header Skeleton -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                <Rectangle Style="{StaticResource SkeletonRectangle}" 
                          Width="200" Height="24" Margin="0,0,20,0"/>
                <Rectangle Style="{StaticResource SkeletonRectangle}" 
                          Width="100" Height="24"/>
            </StackPanel>

            <!-- Stats Cards Skeleton -->
            <UniformGrid Columns="4" Margin="0,0,0,20">
                <Border Background="White" CornerRadius="8" Margin="5" Padding="20">
                    <StackPanel>
                        <Rectangle Style="{StaticResource SkeletonRectangle}" 
                                  Width="80" Height="16" Margin="0,0,0,10"/>
                        <Rectangle Style="{StaticResource SkeletonRectangle}" 
                                  Width="120" Height="28"/>
                    </StackPanel>
                </Border>
                <Border Background="White" CornerRadius="8" Margin="5" Padding="20">
                    <StackPanel>
                        <Rectangle Style="{StaticResource SkeletonRectangle}" 
                                  Width="80" Height="16" Margin="0,0,0,10"/>
                        <Rectangle Style="{StaticResource SkeletonRectangle}" 
                                  Width="120" Height="28"/>
                    </StackPanel>
                </Border>
                <Border Background="White" CornerRadius="8" Margin="5" Padding="20">
                    <StackPanel>
                        <Rectangle Style="{StaticResource SkeletonRectangle}" 
                                  Width="80" Height="16" Margin="0,0,0,10"/>
                        <Rectangle Style="{StaticResource SkeletonRectangle}" 
                                  Width="120" Height="28"/>
                    </StackPanel>
                </Border>
                <Border Background="White" CornerRadius="8" Margin="5" Padding="20">
                    <StackPanel>
                        <Rectangle Style="{StaticResource SkeletonRectangle}" 
                                  Width="80" Height="16" Margin="0,0,0,10"/>
                        <Rectangle Style="{StaticResource SkeletonRectangle}" 
                                  Width="120" Height="28"/>
                    </StackPanel>
                </Border>
            </UniformGrid>

            <!-- Table Skeleton -->
            <Border Background="White" CornerRadius="8" Padding="20">
                <StackPanel>
                    <!-- Table Header -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Rectangle Grid.Column="0" Style="{StaticResource SkeletonRectangle}" 
                                  Height="16" Margin="0,0,10,0"/>
                        <Rectangle Grid.Column="1" Style="{StaticResource SkeletonRectangle}" 
                                  Height="16" Margin="0,0,10,0"/>
                        <Rectangle Grid.Column="2" Style="{StaticResource SkeletonRectangle}" 
                                  Height="16" Margin="0,0,10,0"/>
                        <Rectangle Grid.Column="3" Style="{StaticResource SkeletonRectangle}" 
                                  Height="16"/>
                    </Grid>

                    <!-- Table Rows -->
                    <ItemsControl x:Name="SkeletonRows">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Rectangle Grid.Column="0" Style="{StaticResource SkeletonRectangle}" 
                                              Height="20" Margin="0,0,10,0"/>
                                    <Rectangle Grid.Column="1" Style="{StaticResource SkeletonRectangle}" 
                                              Height="20" Margin="0,0,10,0"/>
                                    <Rectangle Grid.Column="2" Style="{StaticResource SkeletonRectangle}" 
                                              Height="20" Margin="0,0,10,0"/>
                                    <Rectangle Grid.Column="3" Style="{StaticResource SkeletonRectangle}" 
                                              Height="20"/>
                                </Grid>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </Border>
        </StackPanel>
    </Grid>

    <UserControl.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ShimmerAnimation}"/>
        </EventTrigger>
    </UserControl.Triggers>
</UserControl>
