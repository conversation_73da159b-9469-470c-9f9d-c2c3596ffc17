 
 using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة مراقبة الأداء لتتبع أداء التطبيق
    /// </summary>
    public interface IPerformanceMonitoringService
    {
        void StartOperation(string operationName);
        void EndOperation(string operationName);
        Task<T> MeasureAsync<T>(string operationName, Func<Task<T>> operation);
        void MeasureSync(string operationName, Action operation);
        PerformanceMetrics GetMetrics(string operationName);
        void LogMemoryUsage();
        void ClearMetrics();
    }

    public class PerformanceMetrics
    {
        public string OperationName { get; set; } = string.Empty;
        public long TotalExecutions { get; set; }
        public double AverageExecutionTime { get; set; }
        public double MinExecutionTime { get; set; }
        public double MaxExecutionTime { get; set; }
        public double TotalExecutionTime { get; set; }
        public DateTime LastExecuted { get; set; }
    }

    public class PerformanceMonitoringService : IPerformanceMonitoringService
    {
        private readonly ConcurrentDictionary<string, Stopwatch> _activeOperations = new();
        private readonly ConcurrentDictionary<string, PerformanceMetrics> _metrics = new();
        private readonly object _lockObject = new();

        public void StartOperation(string operationName)
        {
            var stopwatch = Stopwatch.StartNew();
            _activeOperations[operationName] = stopwatch;
        }

        public void EndOperation(string operationName)
        {
            if (_activeOperations.TryRemove(operationName, out var stopwatch))
            {
                stopwatch.Stop();
                UpdateMetrics(operationName, stopwatch.Elapsed.TotalMilliseconds);
            }
        }

        public async Task<T> MeasureAsync<T>(string operationName, Func<Task<T>> operation)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var result = await operation();
                return result;
            }
            finally
            {
                stopwatch.Stop();
                UpdateMetrics(operationName, stopwatch.Elapsed.TotalMilliseconds);
            }
        }

        public void MeasureSync(string operationName, Action operation)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                operation();
            }
            finally
            {
                stopwatch.Stop();
                UpdateMetrics(operationName, stopwatch.Elapsed.TotalMilliseconds);
            }
        }

        public PerformanceMetrics GetMetrics(string operationName)
        {
            return _metrics.TryGetValue(operationName, out var metrics) 
                ? metrics 
                : new PerformanceMetrics { OperationName = operationName };
        }

        public void LogMemoryUsage()
        {
            var process = Process.GetCurrentProcess();
            var workingSet = process.WorkingSet64 / (1024 * 1024); // MB
            var privateMemory = process.PrivateMemorySize64 / (1024 * 1024); // MB
            
            System.Diagnostics.Debug.WriteLine($"Memory Usage - Working Set: {workingSet} MB, Private Memory: {privateMemory} MB");
            
            // GC Information
            var gen0 = GC.CollectionCount(0);
            var gen1 = GC.CollectionCount(1);
            var gen2 = GC.CollectionCount(2);
            var totalMemory = GC.GetTotalMemory(false) / (1024 * 1024); // MB
            
            System.Diagnostics.Debug.WriteLine($"GC Collections - Gen0: {gen0}, Gen1: {gen1}, Gen2: {gen2}, Total Memory: {totalMemory} MB");
        }

        public void ClearMetrics()
        {
            _metrics.Clear();
            _activeOperations.Clear();
        }

        private void UpdateMetrics(string operationName, double executionTime)
        {
            lock (_lockObject)
            {
                if (_metrics.TryGetValue(operationName, out var existingMetrics))
                {
                    // تحديث الإحصائيات الموجودة
                    existingMetrics.TotalExecutions++;
                    existingMetrics.TotalExecutionTime += executionTime;
                    existingMetrics.AverageExecutionTime = existingMetrics.TotalExecutionTime / existingMetrics.TotalExecutions;
                    existingMetrics.MinExecutionTime = Math.Min(existingMetrics.MinExecutionTime, executionTime);
                    existingMetrics.MaxExecutionTime = Math.Max(existingMetrics.MaxExecutionTime, executionTime);
                    existingMetrics.LastExecuted = DateTime.UtcNow;
                }
                else
                {
                    // إنشاء إحصائيات جديدة
                    _metrics[operationName] = new PerformanceMetrics
                    {
                        OperationName = operationName,
                        TotalExecutions = 1,
                        TotalExecutionTime = executionTime,
                        AverageExecutionTime = executionTime,
                        MinExecutionTime = executionTime,
                        MaxExecutionTime = executionTime,
                        LastExecuted = DateTime.UtcNow
                    };
                }
            }

            // تسجيل العمليات البطيئة
            if (executionTime > 1000) // أكثر من ثانية
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ Slow Operation: {operationName} took {executionTime:F2}ms");
            }
        }
    }

    /// <summary>
    /// Extension methods لتسهيل استخدام Performance Monitoring
    /// </summary>
    public static class PerformanceExtensions
    {
        public static async Task<T> WithPerformanceMonitoringAsync<T>(
            this Task<T> task, 
            IPerformanceMonitoringService performanceService, 
            string operationName)
        {
            return await performanceService.MeasureAsync(operationName, () => task);
        }

        public static void WithPerformanceMonitoring(
            this Action action, 
            IPerformanceMonitoringService performanceService, 
            string operationName)
        {
            performanceService.MeasureSync(operationName, action);
        }
    }

    /// <summary>
    /// Performance Benchmarks للعمليات الشائعة
    /// </summary>
    public static class PerformanceBenchmarks
    {
        // معايير الأداء المقبولة (بالميلي ثانية)
        public const double DatabaseQueryBenchmark = 100;
        public const double FileOperationBenchmark = 200;
        public const double UIUpdateBenchmark = 16; // 60 FPS
        public const double NetworkOperationBenchmark = 1000;
        public const double ReportGenerationBenchmark = 2000;

        public static bool IsPerformanceAcceptable(string operationName, double executionTime)
        {
            return operationName.ToLower() switch
            {
                var name when name.Contains("database") || name.Contains("query") => executionTime <= DatabaseQueryBenchmark,
                var name when name.Contains("file") || name.Contains("io") => executionTime <= FileOperationBenchmark,
                var name when name.Contains("ui") || name.Contains("render") => executionTime <= UIUpdateBenchmark,
                var name when name.Contains("network") || name.Contains("http") => executionTime <= NetworkOperationBenchmark,
                var name when name.Contains("report") || name.Contains("export") => executionTime <= ReportGenerationBenchmark,
                _ => executionTime <= 500 // معيار عام
            };
        }

        public static string GetPerformanceRating(string operationName, double executionTime)
        {
            var benchmark = GetBenchmarkForOperation(operationName);
            var ratio = executionTime / benchmark;

            return ratio switch
            {
                <= 0.5 => "ممتاز ⚡",
                <= 1.0 => "جيد ✅",
                <= 2.0 => "مقبول ⚠️",
                _ => "بطيء 🐌"
            };
        }

        private static double GetBenchmarkForOperation(string operationName)
        {
            return operationName.ToLower() switch
            {
                var name when name.Contains("database") || name.Contains("query") => DatabaseQueryBenchmark,
                var name when name.Contains("file") || name.Contains("io") => FileOperationBenchmark,
                var name when name.Contains("ui") || name.Contains("render") => UIUpdateBenchmark,
                var name when name.Contains("network") || name.Contains("http") => NetworkOperationBenchmark,
                var name when name.Contains("report") || name.Contains("export") => ReportGenerationBenchmark,
                _ => 500
            };
        }
    }
}
