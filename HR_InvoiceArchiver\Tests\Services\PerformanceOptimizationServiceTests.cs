using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using Moq;

namespace HR_InvoiceArchiver.Tests.Services
{
    public class PerformanceOptimizationServiceTests : IDisposable
    {
        private readonly DatabaseContext _context;
        private readonly PerformanceOptimizationService _performanceService;
        private readonly Mock<ILoggingService> _mockLoggingService;
        private readonly Mock<ISettingsService> _mockSettingsService;

        public PerformanceOptimizationServiceTests()
        {
            // إعداد قاعدة بيانات في الذاكرة للاختبار
            var options = new DbContextOptionsBuilder<DatabaseContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new DatabaseContext(options);
            _mockLoggingService = new Mock<ILoggingService>();
            _mockSettingsService = new Mock<ISettingsService>();

            // إعداد الإعدادات الافتراضية
            var defaultSettings = new HR_InvoiceArchiver.Models.SettingsModel();
            defaultSettings.ResetToDefaults();
            _mockSettingsService.Setup(x => x.LoadSettingsAsync())
                .ReturnsAsync(defaultSettings);

            _performanceService = new PerformanceOptimizationService(
                _context,
                _mockLoggingService.Object,
                _mockSettingsService.Object);

            // إضافة بيانات تجريبية
            SeedTestData();
        }

        private void SeedTestData()
        {
            // إضافة موردين تجريبيين
            var suppliers = new[]
            {
                new HR_InvoiceArchiver.Models.Supplier { Name = "مورد 1", Email = "<EMAIL>", IsActive = true },
                new HR_InvoiceArchiver.Models.Supplier { Name = "مورد 2", Email = "<EMAIL>", IsActive = true }
            };
            _context.Suppliers.AddRange(suppliers);

            // إضافة فواتير تجريبية
            var invoices = new[]
            {
                new HR_InvoiceArchiver.Models.Invoice 
                { 
                    InvoiceNumber = "INV001", 
                    InvoiceDate = DateTime.Now.AddDays(-10),
                    Amount = 1000,
                    SupplierId = 1,
                    Status = HR_InvoiceArchiver.Models.InvoiceStatus.Paid
                },
                new HR_InvoiceArchiver.Models.Invoice 
                { 
                    InvoiceNumber = "INV002", 
                    InvoiceDate = DateTime.Now.AddDays(-5),
                    Amount = 2000,
                    SupplierId = 2,
                    Status = HR_InvoiceArchiver.Models.InvoiceStatus.Pending
                }
            };
            _context.Invoices.AddRange(invoices);

            // إضافة مدفوعات تجريبية
            var payments = new[]
            {
                new HR_InvoiceArchiver.Models.Payment
                {
                    InvoiceId = 1,
                    Amount = 1000,
                    PaymentDate = DateTime.Now.AddDays(-8),
                    Method = PaymentMethod.Cash
                }
            };
            _context.Payments.AddRange(payments);

            _context.SaveChanges();
        }

        [Fact]
        public async Task AnalyzeDatabasePerformanceAsync_ShouldReturnValidReport()
        {
            // Act
            var report = await _performanceService.AnalyzeDatabasePerformanceAsync();

            // Assert
            report.Should().NotBeNull();
            report.GeneratedAt.Should().BeCloseTo(DateTime.Now, TimeSpan.FromMinutes(1));
            report.TotalTables.Should().BeGreaterThan(0);
            report.TablePerformance.Should().NotBeEmpty();
            report.OverallScore.Should().BeDefined();
        }

        [Fact]
        public async Task CreateOptimalIndexesAsync_ShouldReturnTrue()
        {
            // Act
            var result = await _performanceService.CreateOptimalIndexesAsync();

            // Assert
            result.Should().BeTrue();
            _mockLoggingService.Verify(
                x => x.LogInformationAsync(It.Is<string>(s => s.Contains("تم إنشاء") && s.Contains("فهرس")), It.IsAny<LogCategory>(), It.IsAny<object>()),
                Times.Once);
        }

        [Fact]
        public async Task UpdateDatabaseStatisticsAsync_ShouldReturnTrue()
        {
            // Act
            var result = await _performanceService.UpdateDatabaseStatisticsAsync();

            // Assert
            result.Should().BeTrue();
            _mockLoggingService.Verify(
                x => x.LogInformationAsync("تم تحديث إحصائيات قاعدة البيانات", It.IsAny<LogCategory>(), It.IsAny<object>()),
                Times.Once);
        }

        [Fact]
        public async Task CleanupDatabaseAsync_ShouldReturnValidResult()
        {
            // Act
            var result = await _performanceService.CleanupDatabaseAsync();

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Duration.Should().BeGreaterThan(TimeSpan.Zero);
            result.CleanedTables.Should().NotBeNull();
        }

        [Fact]
        public async Task CompressDatabaseAsync_ShouldReturnTrue()
        {
            // Act
            var result = await _performanceService.CompressDatabaseAsync();

            // Assert
            result.Should().BeTrue();
            _mockLoggingService.Verify(
                x => x.LogInformationAsync("تم ضغط قاعدة البيانات", It.IsAny<LogCategory>(), It.IsAny<object>()),
                Times.Once);
        }

        [Fact]
        public async Task AnalyzeSlowQueriesAsync_ShouldReturnList()
        {
            // Act
            var result = await _performanceService.AnalyzeSlowQueriesAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeOfType<System.Collections.Generic.List<SlowQueryInfo>>();
        }

        [Fact]
        public async Task OptimizeQueryAsync_ShouldReturnValidResult()
        {
            // Arrange
            var testQuery = "SELECT * FROM Invoices WHERE SupplierId = 1";

            // Act
            var result = await _performanceService.OptimizeQueryAsync(testQuery);

            // Assert
            result.Should().NotBeNull();
            result.OriginalQuery.Should().Be(testQuery);
            result.OptimizedQuery.Should().NotBeNullOrEmpty();
            result.Success.Should().BeTrue();
            result.AppliedOptimizations.Should().NotBeEmpty();
        }

        [Fact]
        public async Task GetMemoryUsageInfoAsync_ShouldReturnValidInfo()
        {
            // Act
            var result = await _performanceService.GetMemoryUsageInfoAsync();

            // Assert
            result.Should().NotBeNull();
            result.TotalMemoryBytes.Should().BeGreaterThan(0);
            result.UsedMemoryBytes.Should().BeGreaterThan(0);
            result.UsagePercentage.Should().BeInRange(0, 100);
            result.GCCollections.Should().BeGreaterOrEqualTo(0);
            result.MemoryRecommendations.Should().NotBeNull();
        }

        [Fact]
        public async Task ClearCacheAsync_ShouldCompleteSuccessfully()
        {
            // Act & Assert - should not throw
            await _performanceService.ClearCacheAsync();

            _mockLoggingService.Verify(
                x => x.LogInformationAsync("تم تنظيف ذاكرة التخزين المؤقت", It.IsAny<LogCategory>(), It.IsAny<object>()),
                Times.Once);
        }

        [Fact]
        public async Task OptimizeCacheSizeAsync_ShouldCompleteSuccessfully()
        {
            // Act & Assert - should not throw
            await _performanceService.OptimizeCacheSizeAsync();

            _mockLoggingService.Verify(
                x => x.LogInformationAsync("تم تحسين حجم ذاكرة التخزين المؤقت", It.IsAny<LogCategory>(), It.IsAny<object>()),
                Times.Once);
        }

        [Fact]
        public async Task AnalyzeApplicationPerformanceAsync_ShouldReturnValidReport()
        {
            // Act
            var result = await _performanceService.AnalyzeApplicationPerformanceAsync();

            // Assert
            result.Should().NotBeNull();
            result.GeneratedAt.Should().BeCloseTo(DateTime.Now, TimeSpan.FromMinutes(1));
            result.Uptime.Should().BeGreaterThan(TimeSpan.Zero);
            result.MemoryUsage.Should().NotBeNull();
            result.ActiveConnections.Should().BeGreaterOrEqualTo(0);
            result.AverageResponseTimeMs.Should().BeGreaterOrEqualTo(0);
            result.TotalRequests.Should().BeGreaterOrEqualTo(0);
            result.ErrorCount.Should().BeGreaterOrEqualTo(0);
            result.ErrorRate.Should().BeInRange(0, 100);
            result.PerformanceIssues.Should().NotBeNull();
            result.Recommendations.Should().NotBeNull();
            result.OverallScore.Should().BeDefined();
        }

        [Fact]
        public async Task OptimizeConnectionSettingsAsync_ShouldReturnTrue()
        {
            // Act
            var result = await _performanceService.OptimizeConnectionSettingsAsync();

            // Assert
            result.Should().BeTrue();
            _mockLoggingService.Verify(
                x => x.LogInformationAsync("تم تحسين إعدادات الاتصال بقاعدة البيانات", It.IsAny<LogCategory>(), It.IsAny<object>()),
                Times.Once);
        }

        [Fact]
        public async Task ScheduleAutomaticOptimizationAsync_ShouldCompleteSuccessfully()
        {
            // Act & Assert - should not throw
            await _performanceService.ScheduleAutomaticOptimizationAsync();

            _mockLoggingService.Verify(
                x => x.LogInformationAsync("تم تفعيل جدولة التحسين التلقائي", It.IsAny<LogCategory>(), It.IsAny<object>()),
                Times.Once);
        }

        [Fact]
        public async Task UnscheduleAutomaticOptimizationAsync_ShouldCompleteSuccessfully()
        {
            // Act & Assert - should not throw
            await _performanceService.UnscheduleAutomaticOptimizationAsync();

            _mockLoggingService.Verify(
                x => x.LogInformationAsync("تم إلغاء جدولة التحسين التلقائي", It.IsAny<LogCategory>(), It.IsAny<object>()),
                Times.Once);
        }

        [Fact]
        public async Task AnalyzeDatabasePerformanceAsync_WithEmptyDatabase_ShouldHandleGracefully()
        {
            // Arrange - clear all data
            _context.Payments.RemoveRange(_context.Payments);
            _context.Invoices.RemoveRange(_context.Invoices);
            _context.Suppliers.RemoveRange(_context.Suppliers);
            await _context.SaveChangesAsync();

            // Act
            var report = await _performanceService.AnalyzeDatabasePerformanceAsync();

            // Assert
            report.Should().NotBeNull();
            report.TablePerformance.Should().NotBeNull();
            report.Recommendations.Should().NotBeNull();
        }

        [Fact]
        public async Task GetMemoryUsageInfoAsync_ShouldProvideRecommendationsForHighUsage()
        {
            // Act
            var result = await _performanceService.GetMemoryUsageInfoAsync();

            // Assert
            result.Should().NotBeNull();
            
            // إذا كان الاستخدام مرتفع، يجب أن تكون هناك توصيات
            if (result.UsagePercentage > 80)
            {
                result.MemoryRecommendations.Should().NotBeEmpty();
                result.MemoryRecommendations.Should().Contain(r => r.Contains("استخدام الذاكرة مرتفع"));
            }
        }

        [Fact]
        public async Task CleanupDatabaseAsync_ShouldHandleErrors()
        {
            // Arrange - dispose context to cause error
            _context.Dispose();

            // Act
            var result = await _performanceService.CleanupDatabaseAsync();

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.ErrorMessage.Should().NotBeNullOrEmpty();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
