using System;
using System.Windows;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة إدارة الأخطاء لتقليل الرسائل المزعجة وتحسين تجربة المستخدم
    /// </summary>
    public interface IErrorHandlingService
    {
        void HandleError(Exception ex, string context, bool showToUser = false);
        void HandleValidationError(string message, string context);
        void HandleCriticalError(Exception ex, string context);
        bool ShouldShowToUser(Exception ex);
    }

    public class ErrorHandlingService : IErrorHandlingService
    {
        private readonly IToastService _toastService;

        public ErrorHandlingService(IToastService toastService)
        {
            _toastService = toastService;
        }

        /// <summary>
        /// معالجة الأخطاء العامة - تقليل MessageBox واستخدام Toast
        /// </summary>
        public void HandleError(Exception ex, string context, bool showToUser = false)
        {
            // تسجيل الخطأ في Debug
            System.Diagnostics.Debug.WriteLine($"[{context}] خطأ: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

            if (showToUser || ShouldShowToUser(ex))
            {
                var message = GetUserFriendlyMessage(ex, context);
                
                // محاولة إظهار Toast أولاً
                try
                {
                    _toastService?.ShowError("خطأ", message);
                    return; // نجح Toast، لا نحتاج MessageBox
                }
                catch
                {
                    // فشل Toast، نظهر MessageBox للأخطاء الحرجة فقط
                    if (IsCriticalError(ex))
                    {
                        MessageBox.Show(message, "خطأ حرج", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        /// <summary>
        /// معالجة أخطاء التحقق من صحة البيانات - استخدام Toast فقط
        /// </summary>
        public void HandleValidationError(string message, string context)
        {
            System.Diagnostics.Debug.WriteLine($"[{context}] خطأ في التحقق: {message}");
            
            try
            {
                _toastService?.ShowWarning("تحقق من البيانات", message);
            }
            catch
            {
                // لا نظهر MessageBox لأخطاء التحقق
                System.Diagnostics.Debug.WriteLine("Failed to show validation error toast");
            }
        }

        /// <summary>
        /// معالجة الأخطاء الحرجة التي تتطلب تدخل المستخدم
        /// </summary>
        public void HandleCriticalError(Exception ex, string context)
        {
            System.Diagnostics.Debug.WriteLine($"[{context}] خطأ حرج: {ex.Message}");
            
            var message = GetUserFriendlyMessage(ex, context);
            
            try
            {
                _toastService?.ShowError("خطأ حرج", message);
            }
            catch
            {
                // للأخطاء الحرجة، نظهر MessageBox كحل أخير
                MessageBox.Show(message, "خطأ حرج", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديد ما إذا كان يجب إظهار الخطأ للمستخدم
        /// </summary>
        public bool ShouldShowToUser(Exception ex)
        {
            return ex switch
            {
                // أخطاء قاعدة البيانات
                System.Data.Common.DbException => true,
                
                // أخطاء الملفات
                System.IO.FileNotFoundException => true,
                System.UnauthorizedAccessException => true,
                System.IO.DirectoryNotFoundException => true,
                
                // أخطاء الشبكة
                System.Net.NetworkInformation.NetworkInformationException => true,
                System.Net.WebException => true,
                
                // أخطاء التحقق
                ArgumentNullException => false, // خطأ برمجي، لا نظهره للمستخدم
                ArgumentException => true,
                
                // أخطاء النظام الحرجة
                OutOfMemoryException => true,
                System.ComponentModel.Win32Exception => true,
                
                // الأخطاء الأخرى لا نظهرها افتراضياً
                _ => false
            };
        }

        /// <summary>
        /// تحديد ما إذا كان الخطأ حرجاً
        /// </summary>
        private bool IsCriticalError(Exception ex)
        {
            return ex switch
            {
                OutOfMemoryException => true,
                System.ComponentModel.Win32Exception => true,
                TypeInitializationException => true,
                System.Configuration.ConfigurationErrorsException => true,
                _ => false
            };
        }

        /// <summary>
        /// الحصول على رسالة مفهومة للمستخدم
        /// </summary>
        private string GetUserFriendlyMessage(Exception ex, string context)
        {
            return ex switch
            {
                System.Data.Common.DbException => "حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى.",
                System.IO.FileNotFoundException => "لم يتم العثور على الملف المطلوب.",
                System.UnauthorizedAccessException => "ليس لديك صلاحية للوصول إلى هذا المورد.",
                System.Net.WebException => "حدث خطأ في الاتصال بالشبكة.",
                ArgumentException => "البيانات المدخلة غير صحيحة.",
                OutOfMemoryException => "الذاكرة غير كافية لإتمام العملية.",
                _ => $"حدث خطأ في {context}. يرجى المحاولة مرة أخرى."
            };
        }
    }

    /// <summary>
    /// Extension methods لتسهيل استخدام خدمة معالجة الأخطاء
    /// </summary>
    public static class ErrorHandlingExtensions
    {
        public static void HandleError(this IErrorHandlingService errorService, Exception ex, string context)
        {
            errorService.HandleError(ex, context, false);
        }

        public static void HandleUserError(this IErrorHandlingService errorService, Exception ex, string context)
        {
            errorService.HandleError(ex, context, true);
        }
    }
}
