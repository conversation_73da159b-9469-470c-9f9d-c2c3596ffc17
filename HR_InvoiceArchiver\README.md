# نظام أرشفة فواتير الموارد البشرية (HR Invoice Archiver)

## نظرة عامة

نظام أرشفة فواتير الموارد البشرية هو تطبيق سطح مكتب متطور مبني بتقنية WPF و .NET 8، مصمم لإدارة وأرشفة فواتير الموردين والمدفوعات بطريقة فعالة ومنظمة. يدعم النظام اللغة العربية بالكامل ويوفر واجهة مستخدم حديثة باستخدام Material Design.

## الميزات الرئيسية

### 📋 إدارة الفواتير
- إضافة وتعديل وحذف الفواتير
- تتبع حالة الفواتير (غير مسددة، مسددة جزئياً، مسددة)
- تحديد تواريخ الاستحقاق وتتبع الفواتير المتأخرة
- إرفاق ملفات مع الفواتير (PDF, صور، مستندات)

### 💰 إدارة المدفوعات
- تسجيل المدفوعات الفردية والمتعددة
- نظام الدفع المتعدد الذكي مع خصم تلقائي
- دعم طرق دفع متعددة (نقدي، شيك، تحويل بنكي، بطاقة ائتمان)
- تتبع المبالغ المدفوعة والمتبقية

### 🏢 إدارة الموردين
- قاعدة بيانات شاملة للموردين
- معلومات الاتصال والعناوين
- إحصائيات مفصلة لكل مورد
- تتبع الأداء المالي للموردين

### ☁️ التخزين السحابي
- نسخ احتياطي تلقائي إلى Google Drive
- مزامنة الملفات والمرفقات
- حماية البيانات من الفقدان

### 📊 التقارير والإحصائيات
- لوحة تحكم تفاعلية مع رسوم بيانية
- تقارير مفصلة للفواتير والمدفوعات والموردين
- تقارير شهرية وسنوية
- إحصائيات الأداء المالي

### 🔍 البحث والتصفية
- بحث متقدم في جميع البيانات
- فلاتر متعددة حسب التاريخ والحالة والمورد
- ترقيم الصفحات للبيانات الكبيرة

## المتطلبات التقنية

### متطلبات النظام
- Windows 10 أو أحدث
- .NET 8.0 Runtime
- 4 GB RAM (الحد الأدنى)
- 500 MB مساحة تخزين فارغة

### التقنيات المستخدمة
- **الإطار**: .NET 8.0 WPF
- **قاعدة البيانات**: SQLite مع Entity Framework Core 8.0
- **واجهة المستخدم**: Material Design in XAML
- **التخزين السحابي**: Google Drive API v3
- **التقارير**: LiveCharts.Wpf
- **تصدير البيانات**: ClosedXML, iTextSharp
- **الاختبارات**: xUnit, Moq, FluentAssertions

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/HR_InvoiceArchiver.git
cd HR_InvoiceArchiver
```

### 2. استعادة الحزم
```bash
dotnet restore
```

### 3. بناء المشروع
```bash
dotnet build
```

### 4. تشغيل التطبيق
```bash
dotnet run
```

### 5. إعداد التخزين السحابي (اختياري)
1. إنشاء مشروع في Google Cloud Console
2. تفعيل Google Drive API
3. تحميل ملف `credentials.json`
4. وضع الملف في مجلد التطبيق

## الاستخدام

### البدء السريع
1. تشغيل التطبيق لأول مرة
2. إضافة مورد جديد من صفحة الموردين
3. إنشاء فاتورة جديدة وربطها بالمورد
4. تسجيل المدفوعات عند الحاجة

### الدفع المتعدد
1. الانتقال إلى صفحة المدفوعات
2. اختيار "إضافة وصل متعدد"
3. اختيار المورد والفواتير المراد تسديدها
4. النظام سيحسب الخصم تلقائياً
5. إدخال تفاصيل الدفع وحفظ الوصل

### نظام الخصم الذكي
- **3-4 فواتير**: خصم 1%
- **5-9 فواتير**: خصم 3%
- **10+ فواتير**: خصم 5%
- **مبلغ 10 مليون دينار فأكثر**: خصم إضافي 3%
- **الحد الأقصى للخصم**: 15%

## بنية المشروع

```
HR_InvoiceArchiver/
├── Models/                 # نماذج البيانات
├── Data/                   # قاعدة البيانات والمستودعات
├── Services/               # خدمات العمل
├── Pages/                  # صفحات التطبيق
├── Windows/                # النوافذ المنبثقة
├── Controls/               # التحكمات المخصصة
├── Converters/             # محولات البيانات
├── Utils/                  # الأدوات المساعدة
├── Styles/                 # أنماط واجهة المستخدم
├── Resources/              # الموارد والأيقونات
├── Tests/                  # الاختبارات
└── Documentation/          # التوثيق
```

## الاختبارات

### تشغيل الاختبارات
```bash
dotnet test
```

### تغطية الكود
```bash
dotnet test --collect:"XPlat Code Coverage"
```

### أنواع الاختبارات
- **اختبارات الوحدة**: للنماذج والخدمات
- **اختبارات التكامل**: للمستودعات وقاعدة البيانات
- **اختبارات واجهة المستخدم**: للتحكمات والصفحات

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

### معايير الكود
- اتباع معايير C# الرسمية
- كتابة اختبارات للميزات الجديدة
- توثيق الكود باللغة العربية
- اتباع نمط MVVM في واجهة المستخدم

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم والمساعدة

### الإبلاغ عن المشاكل
يرجى استخدام [GitHub Issues](https://github.com/your-repo/HR_InvoiceArchiver/issues) للإبلاغ عن المشاكل أو طلب ميزات جديدة.

### التواصل
- البريد الإلكتروني: <EMAIL>
- التوثيق: [Wiki](https://github.com/your-repo/HR_InvoiceArchiver/wiki)

## خارطة الطريق

### الإصدار القادم (v2.0)
- [ ] نظام المصادقة والتفويض
- [ ] دعم قواعد بيانات متعددة
- [ ] API للتكامل مع أنظمة خارجية
- [ ] تطبيق ويب مصاحب
- [ ] دعم العملات المتعددة

### الميزات المستقبلية
- [ ] تكامل مع أنظمة المحاسبة
- [ ] إشعارات البريد الإلكتروني
- [ ] تقارير متقدمة مع AI
- [ ] تطبيق موبايل

## شكر وتقدير

- فريق Material Design in XAML
- مجتمع .NET المفتوح المصدر
- جميع المساهمين في المشروع

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. يرجى التحقق من التحديثات بانتظام.
