﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <StartupObject>HR_InvoiceArchiver.App</StartupObject>
    <!-- إخفاء تحذيرات الحزم الخارجية القديمة -->
    <NoWarn>$(NoWarn);NU1701</NoWarn>
    <!-- تحسين الأداء -->
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="EPPlus" Version="7.0.0" />
    <!-- الإبقاء على iTextSharp مؤقتاً لتجنب كسر الكود -->
    <PackageReference Include="iTextSharp" Version="5.5.13.4" />
    <!-- الإبقاء على LiveCharts القديم مؤقتاً لتجنب كسر الكود -->
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
    <PackageReference Include="MaterialDesignColors" Version="5.2.1" />
    <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />

    <!-- Entity Framework Core - أحدث إصدار مستقر -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.11">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />

    <!-- Google Drive API للنسخ الاحتياطي السحابي - أحدث إصدار مستقر -->
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.70.0.3834" />
    <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

    <!-- Testing Packages -->
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.11" />
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

</Project>
