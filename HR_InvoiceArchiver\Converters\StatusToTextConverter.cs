using System;
using System.Globalization;
using System.Windows.Data;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Converters
{
    public class StatusToTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is InvoiceStatus status)
            {
                return status switch
                {
                    InvoiceStatus.Unpaid => "غير مسددة",
                    InvoiceStatus.PartiallyPaid => "تسديد جزئي",
                    InvoiceStatus.Paid => "مسددة",
                    _ => "غير محدد"
                };
            }
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string text)
            {
                return text switch
                {
                    "غير مسددة" => InvoiceStatus.Unpaid,
                    "تسديد جزئي" => InvoiceStatus.PartiallyPaid,
                    "مسددة" => InvoiceStatus.Paid,
                    _ => InvoiceStatus.Unpaid
                };
            }
            return InvoiceStatus.Unpaid;
        }
    }
}
