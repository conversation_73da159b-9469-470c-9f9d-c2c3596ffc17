<Window x:Class="HR_InvoiceArchiver.Windows.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام أرشفة فواتير الموارد البشرية"
        Height="600" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <Style x:Key="LoginCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="32"/>
            <Setter Property="Margin" Value="16"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="{DynamicResource Primary200}" Offset="0"/>
                <GradientStop Color="{DynamicResource Primary400}" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <materialDesign:Card Style="{StaticResource LoginCardStyle}" 
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Width="350">
            <StackPanel>
                <!-- Logo and Title -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,32">
                    <materialDesign:PackIcon Kind="Security" 
                                           Width="64" Height="64"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,16"/>
                    
                    <TextBlock Text="تسجيل الدخول"
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBody}"/>
                    
                    <TextBlock Text="نظام أرشفة فواتير الموارد البشرية"
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             HorizontalAlignment="Center"
                             Opacity="0.7"
                             Margin="0,4,0,0"/>
                </StackPanel>

                <!-- Login Form -->
                <StackPanel>
                    <!-- Username -->
                    <TextBox x:Name="UsernameTextBox"
                           materialDesign:HintAssist.Hint="اسم المستخدم"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           Margin="0,0,0,16"
                           Text="admin">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <!-- Password -->
                    <PasswordBox x:Name="PasswordBox"
                               materialDesign:HintAssist.Hint="كلمة المرور"
                               Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                               Margin="0,0,0,16">
                        <PasswordBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                        </PasswordBox.InputBindings>
                    </PasswordBox>

                    <!-- Remember Me -->
                    <CheckBox x:Name="RememberMeCheckBox"
                            Content="تذكرني"
                            Style="{StaticResource MaterialDesignCheckBox}"
                            Margin="0,0,0,24"/>

                    <!-- Login Button -->
                    <Button x:Name="LoginButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Height="40"
                          Margin="0,0,0,16"
                          Click="LoginButton_Click"
                          IsDefault="True">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Login" 
                                                       Margin="0,0,8,0"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="تسجيل الدخول" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <!-- Forgot Password -->
                    <Button x:Name="ForgotPasswordButton"
                          Content="نسيت كلمة المرور؟"
                          Style="{StaticResource MaterialDesignFlatButton}"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,16"
                          Click="ForgotPasswordButton_Click"/>

                    <!-- Status Message -->
                    <Border x:Name="StatusBorder"
                          Background="{DynamicResource ValidationErrorBrush}"
                          CornerRadius="4"
                          Padding="12,8"
                          Margin="0,0,0,16"
                          Visibility="Collapsed">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                   Width="16" Height="16"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="StatusTextBlock"
                                     Text=""
                                     Foreground="White"
                                     VerticalAlignment="Center"
                                     TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>

                    <!-- Loading Indicator -->
                    <ProgressBar x:Name="LoadingProgressBar"
                               Style="{StaticResource MaterialDesignLinearProgressBar}"
                               IsIndeterminate="True"
                               Height="4"
                               Visibility="Collapsed"
                               Margin="0,0,0,16"/>
                </StackPanel>

                <!-- Footer -->
                <StackPanel HorizontalAlignment="Center" 
                          Margin="0,24,0,0"
                          Opacity="0.7">
                    <TextBlock Text="الإصدار 1.0.0"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                    <TextBlock Text="© 2024 جميع الحقوق محفوظة"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"
                             Margin="0,4,0,0"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- Security Features Info -->
        <materialDesign:Card VerticalAlignment="Bottom"
                           HorizontalAlignment="Left"
                           Margin="16"
                           Padding="16"
                           Width="200">
            <StackPanel>
                <TextBlock Text="ميزات الأمان"
                         Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                         Margin="0,0,0,8"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                    <materialDesign:PackIcon Kind="Shield" 
                                           Width="16" Height="16"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="تشفير البيانات"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                    <materialDesign:PackIcon Kind="Lock" 
                                           Width="16" Height="16"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="جلسات آمنة"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Eye" 
                                           Width="16" Height="16"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="مراقبة الأنشطة"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- Help Button -->
        <Button x:Name="HelpButton"
              Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
              VerticalAlignment="Bottom"
              HorizontalAlignment="Right"
              Margin="16"
              Click="HelpButton_Click"
              ToolTip="المساعدة">
            <materialDesign:PackIcon Kind="Help"/>
        </Button>
    </Grid>
</Window>
