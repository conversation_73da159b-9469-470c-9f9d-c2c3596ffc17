# دليل إعداد التخزين السحابي - Google Drive

## 📋 نظرة عامة

يوفر برنامج أرشيف الفواتير إمكانية النسخ الاحتياطي التلقائي للمرفقات على Google Drive. هذا الدليل يوضح كيفية إعداد هذه الخدمة خطوة بخطوة.

## ⚠️ متطلبات مهمة

- حساب Google صالح
- اتصال بالإنترنت
- ملف `credentials.json` من Google Cloud Console

---

## 🚀 خطوات الإعداد

### الخطوة 1: إنشاء مشروع في Google Cloud Console

1. **اذهب إلى Google Cloud Console:**
   ```
   https://console.cloud.google.com
   ```

2. **إنشاء مشروع جديد:**
   - انقر على القائمة المنسدلة للمشاريع في الأعلى
   - اختر "مشروع جديد" (New Project)
   - اكتب اسم المشروع (مثل: `InvoiceArchiver`)
   - انقر على "إنشاء" (Create)

### الخطوة 2: تفعيل Google Drive API

1. **الانتقال إلى مكتبة APIs:**
   - من القائمة الجانبية، اختر `APIs & Services` > `Library`

2. **البحث عن Google Drive API:**
   - في مربع البحث، اكتب: `Google Drive API`
   - انقر على النتيجة الأولى

3. **تفعيل الخدمة:**
   - انقر على زر `Enable`
   - انتظر حتى يتم التفعيل

### الخطوة 3: إنشاء بيانات الاعتماد (OAuth 2.0)

1. **الانتقال إلى صفحة Credentials:**
   - من القائمة الجانبية، اختر `APIs & Services` > `Credentials`

2. **إنشاء OAuth client ID:**
   - انقر على `+ CREATE CREDENTIALS`
   - اختر `OAuth client ID`

3. **تكوين OAuth consent screen (إذا لم يكن مُعد مسبقاً):**
   - اختر `External` كنوع المستخدم
   - املأ المعلومات المطلوبة:
     - اسم التطبيق: `Invoice Archiver` أو `ARSHAFA`
     - البريد الإلكتروني للدعم: بريدك الإلكتروني
     - البريد الإلكتروني للمطور: بريدك الإلكتروني
   - **مهم جداً:** في قسم "Test users"، أضف بريدك الإلكتروني كمستخدم اختبار
   - احفظ واستمر

4. **إنشاء OAuth client:**
   - اختر نوع التطبيق: `Desktop application`
   - اكتب اسم العميل: `InvoiceArchiver Desktop`
   - انقر على `Create`

### الخطوة 4: تحميل ملف credentials.json

1. **تحميل الملف:**
   - بعد إنشاء OAuth client، ستظهر نافذة منبثقة
   - انقر على `DOWNLOAD JSON`
   - احفظ الملف باسم `credentials.json`

2. **وضع الملف في المكان الصحيح:**
   - انسخ ملف `credentials.json`
   - ضعه في نفس مجلد الملف التنفيذي للبرنامج
   - المسار يجب أن يكون: `[مجلد البرنامج]/credentials.json`

### الخطوة 5: إعادة تشغيل البرنامج واختبار الاتصال

1. **إعادة تشغيل البرنامج:**
   - أغلق البرنامج تماماً
   - أعد تشغيله

2. **ربط Google Drive:**
   - انقر على زر "التخزين السحابي" في الشريط الجانبي
   - انقر على "ربط Google Drive"
   - سيتم فتح متصفح لتسجيل الدخول

3. **إعطاء الصلاحيات:**
   - سجل دخول بحساب Google الخاص بك
   - اقرأ الصلاحيات المطلوبة
   - انقر على "السماح" (Allow)

---

## 🔧 استكشاف الأخطاء وإصلاحها

### مشكلة: "ملف credentials.json غير موجود"

**الحل:**
- تأكد من وجود ملف `credentials.json` في مجلد البرنامج
- تأكد من أن اسم الملف صحيح (بدون مسافات إضافية)
- تأكد من أن الملف غير تالف

### مشكلة: "فشل في الاتصال"

**الحل:**
- تحقق من اتصال الإنترنت
- تأكد من أن Google Drive API مُفعل في مشروعك
- جرب إعادة تشغيل البرنامج

### مشكلة: "رفض الوصول" أو "access_denied: 403"

**السبب:** التطبيق في وضع Testing ولم يتم إضافة بريدك الإلكتروني كمستخدم اختبار.

**الحل:**
1. **اذهب إلى OAuth consent screen:**
   - في Google Cloud Console، اختر `APIs & Services` > `OAuth consent screen`

2. **أضف نفسك كمستخدم اختبار:**
   - انتقل إلى قسم "Test users"
   - انقر على `+ ADD USERS`
   - أدخل بريدك الإلكتروني الذي ستستخدمه للدخول
   - انقر على `Save`

3. **أو انشر التطبيق (للاستخدام الشخصي):**
   - في نفس الصفحة، انقر على `PUBLISH APP`
   - اختر `Make External`
   - **تحذير:** هذا يجعل التطبيق متاحاً للجميع، استخدمه فقط إذا كنت تفهم المخاطر

**الحل المفضل:** إضافة بريدك الإلكتروني كمستخدم اختبار (الخيار الأول)

---

## 📁 هيكل الملفات المطلوب

```
[مجلد البرنامج]/
├── HR_InvoiceArchiver.exe
├── credentials.json          ← ملف مطلوب
└── [ملفات أخرى...]
```

---

## 🔒 الأمان والخصوصية

- ملف `credentials.json` يحتوي على معلومات حساسة
- لا تشارك هذا الملف مع أي شخص
- احتفظ بنسخة احتياطية آمنة من الملف
- البرنامج يطلب فقط صلاحيات قراءة وكتابة الملفات في Google Drive

---

## 📞 الدعم

إذا واجهت أي مشاكل:

1. تأكد من اتباع جميع الخطوات بالترتيب
2. تحقق من رسائل الخطأ في البرنامج
3. راجع قسم استكشاف الأخطاء أعلاه
4. تأكد من أن حساب Google الخاص بك يدعم Google Drive

---

## 🎯 ملاحظات إضافية

- عملية الإعداد تحتاج للقيام بها مرة واحدة فقط
- بعد الإعداد الناجح، ستتم المزامنة تلقائياً
- يمكنك قطع الاتصال في أي وقت من نافذة إعدادات التخزين السحابي
- البرنامج ينشئ مجلد خاص به في Google Drive لحفظ المرفقات
