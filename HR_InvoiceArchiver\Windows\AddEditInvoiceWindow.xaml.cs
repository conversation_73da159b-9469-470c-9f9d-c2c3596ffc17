using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;

namespace HR_InvoiceArchiver.Windows
{
    public partial class AddEditInvoiceWindow : Window, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        
        private Invoice? _currentInvoice;
        private bool _isEditMode;
        
        // Binding Properties
        private string _invoiceNumber = string.Empty;
        private DateTime _invoiceDate = DateTime.Now;
        private DateTime? _dueDate;
        private string _description = string.Empty;
        private decimal _totalAmount;
        private InvoiceStatus _status = InvoiceStatus.Unpaid;
        private string _attachmentPath = string.Empty;
        private string _attachmentName = string.Empty;
        private Supplier? _selectedSupplier;
        private ObservableCollection<Supplier> _suppliers = new();
        private ObservableCollection<InvoiceStatus> _statusOptions = new();

        public AddEditInvoiceWindow()
        {
            // Initialize services from DI container
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
            
            InitializeComponent();
            DataContext = this;
            
            InitializeStatusOptions();
            _ = LoadSuppliersAsync();
        }

        public AddEditInvoiceWindow(Invoice invoice) : this()
        {
            _currentInvoice = invoice;
            _isEditMode = true;
            WindowTitleTextBlock.Text = "تعديل الفاتورة";
            LoadInvoiceData();
        }

        #region Properties

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set => SetProperty(ref _invoiceNumber, value);
        }

        public DateTime InvoiceDate
        {
            get => _invoiceDate;
            set => SetProperty(ref _invoiceDate, value);
        }

        public DateTime? DueDate
        {
            get => _dueDate;
            set => SetProperty(ref _dueDate, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set => SetProperty(ref _totalAmount, value);
        }

        public InvoiceStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public string AttachmentPath
        {
            get => _attachmentPath;
            set
            {
                SetProperty(ref _attachmentPath, value);
                AttachmentName = Path.GetFileName(value);
                OnPropertyChanged(nameof(HasAttachment));
            }
        }

        public string AttachmentName
        {
            get => _attachmentName;
            set => SetProperty(ref _attachmentName, value);
        }

        public bool HasAttachment => !string.IsNullOrEmpty(AttachmentPath);

        public Supplier? SelectedSupplier
        {
            get => _selectedSupplier;
            set => SetProperty(ref _selectedSupplier, value);
        }

        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set => SetProperty(ref _suppliers, value);
        }

        public ObservableCollection<InvoiceStatus> StatusOptions
        {
            get => _statusOptions;
            set => SetProperty(ref _statusOptions, value);
        }

        #endregion

        #region Initialization

        private void InitializeStatusOptions()
        {
            StatusOptions = new ObservableCollection<InvoiceStatus>
            {
                InvoiceStatus.Unpaid,
                InvoiceStatus.PartiallyPaid,
                InvoiceStatus.Paid
            };
            
            StatusComboBox.ItemsSource = StatusOptions;
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersAsync();

                // Ensure UI updates happen on UI thread
                await Dispatcher.InvokeAsync(() =>
                {
                    Suppliers = new ObservableCollection<Supplier>(suppliers);
                });
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تحميل قائمة الموردين: {ex.Message}");
            }
        }

        private void LoadInvoiceData()
        {
            if (_currentInvoice == null) return;

            InvoiceNumber = _currentInvoice.InvoiceNumber;
            InvoiceDate = _currentInvoice.InvoiceDate;
            DueDate = _currentInvoice.DueDate;
            Description = _currentInvoice.Description ?? string.Empty;
            TotalAmount = _currentInvoice.Amount;
            Status = _currentInvoice.Status;
            AttachmentPath = _currentInvoice.AttachmentPath ?? string.Empty;
            
            // Set selected supplier after suppliers are loaded
            if (_currentInvoice.SupplierId > 0)
            {
                // Use Dispatcher.BeginInvoke instead of Task.Run to avoid threading issues
                Dispatcher.BeginInvoke(new Action(async () =>
                {
                    await Task.Delay(100); // Wait for suppliers to load
                    SelectedSupplier = Suppliers.FirstOrDefault(s => s.Id == _currentInvoice.SupplierId);
                }));
            }
        }

        #endregion

        #region Event Handlers

        private void BrowseAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار مرفق الفاتورة",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات الصور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|ملفات Word (*.doc;*.docx)|*.doc;*.docx|ملفات Excel (*.xls;*.xlsx)|*.xls;*.xlsx",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                AttachmentPath = openFileDialog.FileName;
            }
        }

        private void RemoveAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            AttachmentPath = string.Empty;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                SaveButton.IsEnabled = false;
                
                var invoice = CreateInvoiceFromInput();
                
                if (_isEditMode && _currentInvoice != null)
                {
                    invoice.Id = _currentInvoice.Id;
                    await _invoiceService.UpdateInvoiceAsync(invoice);
                    _toastService.ShowSuccess("نجح", "تم تحديث الفاتورة بنجاح");
                }
                else
                {
                    await _invoiceService.CreateInvoiceAsync(invoice);
                    _toastService.ShowSuccess("نجح", "تم إنشاء الفاتورة بنجاح");
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في حفظ الفاتورة: {ex.Message}");
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        #endregion

        #region Validation and Data Creation

        private bool ValidateInput()
        {
            ValidationMessageTextBlock.Visibility = Visibility.Collapsed;
            
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(InvoiceNumber))
                errors.Add("رقم الفاتورة مطلوب");

            if (SelectedSupplier == null)
                errors.Add("يجب اختيار المورد");

            if (TotalAmount <= 0)
                errors.Add("المبلغ الإجمالي يجب أن يكون أكبر من صفر");

            if (InvoiceDate > DateTime.Now.AddDays(1))
                errors.Add("تاريخ الفاتورة لا يمكن أن يكون في المستقبل");

            if (DueDate.HasValue && DueDate < InvoiceDate)
                errors.Add("تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الفاتورة");

            if (errors.Any())
            {
                ValidationMessageTextBlock.Text = string.Join("\n", errors);
                ValidationMessageTextBlock.Visibility = Visibility.Visible;
                return false;
            }

            return true;
        }

        private Invoice CreateInvoiceFromInput()
        {
            return new Invoice
            {
                InvoiceNumber = InvoiceNumber.Trim(),
                InvoiceDate = InvoiceDate,
                DueDate = DueDate,
                Description = Description?.Trim(),
                Amount = TotalAmount,
                Status = Status,
                SupplierId = SelectedSupplier!.Id,
                Supplier = SelectedSupplier,
                AttachmentPath = string.IsNullOrWhiteSpace(AttachmentPath) ? null : AttachmentPath.Trim(),
                CreatedDate = _isEditMode ? _currentInvoice!.CreatedDate : DateTime.Now,
                UpdatedDate = DateTime.Now
            };
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
