using System;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Controls
{
    /// <summary>
    /// Pagination Control محسن للأداء
    /// </summary>
    public partial class PaginationControl : UserControl
    {
        public static readonly DependencyProperty CurrentPageProperty =
            DependencyProperty.Register(nameof(CurrentPage), typeof(int), typeof(PaginationControl),
                new PropertyMetadata(1, OnCurrentPageChanged));

        public static readonly DependencyProperty TotalPagesProperty =
            DependencyProperty.Register(nameof(TotalPages), typeof(int), typeof(PaginationControl),
                new PropertyMetadata(1, OnTotalPagesChanged));

        public static readonly DependencyProperty TotalItemsProperty =
            DependencyProperty.Register(nameof(TotalItems), typeof(int), typeof(PaginationControl),
                new PropertyMetadata(0, OnTotalItemsChanged));

        public static readonly DependencyProperty PageSizeProperty =
            DependencyProperty.Register(nameof(PageSize), typeof(int), typeof(PaginationControl),
                new PropertyMetadata(10, OnPageSizeChanged));

        public int CurrentPage
        {
            get => (int)GetValue(CurrentPageProperty);
            set => SetValue(CurrentPageProperty, value);
        }

        public int TotalPages
        {
            get => (int)GetValue(TotalPagesProperty);
            set => SetValue(TotalPagesProperty, value);
        }

        public int TotalItems
        {
            get => (int)GetValue(TotalItemsProperty);
            set => SetValue(TotalItemsProperty, value);
        }

        public int PageSize
        {
            get => (int)GetValue(PageSizeProperty);
            set => SetValue(PageSizeProperty, value);
        }

        // Events
        public event EventHandler<PageChangedEventArgs>? PageChanged;
        public event EventHandler<PageSizeChangedEventArgs>? PageSizeChanged;

        public PaginationControl()
        {
            InitializeComponent();
            UpdateUI();
        }

        private static void OnCurrentPageChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PaginationControl control)
            {
                control.UpdateUI();
            }
        }

        private static void OnTotalPagesChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PaginationControl control)
            {
                control.UpdateUI();
            }
        }

        private static void OnTotalItemsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PaginationControl control)
            {
                control.UpdateUI();
            }
        }

        private static void OnPageSizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PaginationControl control)
            {
                control.UpdatePageSizeComboBox();
                control.UpdateUI();
            }
        }

        private void UpdateUI()
        {
            UpdateNavigationButtons();
            UpdatePageNumbers();
            UpdatePageInfo();
        }

        private void UpdateNavigationButtons()
        {
            FirstPageButton.IsEnabled = CurrentPage > 1;
            PreviousPageButton.IsEnabled = CurrentPage > 1;
            NextPageButton.IsEnabled = CurrentPage < TotalPages;
            LastPageButton.IsEnabled = CurrentPage < TotalPages;
        }

        private void UpdatePageNumbers()
        {
            PageNumbersPanel.Children.Clear();

            if (TotalPages <= 1) return;

            // حساب نطاق الصفحات المعروضة
            int startPage = Math.Max(1, CurrentPage - 2);
            int endPage = Math.Min(TotalPages, CurrentPage + 2);

            // إضافة نقاط في البداية إذا لزم الأمر
            if (startPage > 1)
            {
                AddPageButton(1);
                if (startPage > 2)
                {
                    AddEllipsis();
                }
            }

            // إضافة أرقام الصفحات
            for (int i = startPage; i <= endPage; i++)
            {
                AddPageButton(i);
            }

            // إضافة نقاط في النهاية إذا لزم الأمر
            if (endPage < TotalPages)
            {
                if (endPage < TotalPages - 1)
                {
                    AddEllipsis();
                }
                AddPageButton(TotalPages);
            }
        }

        private void AddPageButton(int pageNumber)
        {
            var button = new Button
            {
                Content = pageNumber.ToString(),
                Style = pageNumber == CurrentPage 
                    ? (Style)FindResource("ActivePageButtonStyle")
                    : (Style)FindResource("PaginationButtonStyle"),
                Tag = pageNumber
            };

            button.Click += PageButton_Click;
            PageNumbersPanel.Children.Add(button);
        }

        private void AddEllipsis()
        {
            var ellipsis = new TextBlock
            {
                Text = "...",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(8, 0, 8, 0),
                Foreground = System.Windows.Media.Brushes.Gray
            };
            PageNumbersPanel.Children.Add(ellipsis);
        }

        private void UpdatePageInfo()
        {
            int startItem = (CurrentPage - 1) * PageSize + 1;
            int endItem = Math.Min(CurrentPage * PageSize, TotalItems);

            PageInfoTextBlock.Text = TotalItems > 0
                ? $"صفحة {CurrentPage} من {TotalPages} (عرض {startItem}-{endItem} من {TotalItems} عنصر)"
                : "لا توجد عناصر";
        }

        private void UpdatePageSizeComboBox()
        {
            foreach (ComboBoxItem item in PageSizeComboBox.Items)
            {
                if (int.TryParse(item.Content?.ToString(), out int size) && size == PageSize)
                {
                    PageSizeComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        // Event Handlers
        private void FirstPage_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentPage > 1)
            {
                PageChanged?.Invoke(this, new PageChangedEventArgs(1));
            }
        }

        private void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentPage > 1)
            {
                PageChanged?.Invoke(this, new PageChangedEventArgs(CurrentPage - 1));
            }
        }

        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentPage < TotalPages)
            {
                PageChanged?.Invoke(this, new PageChangedEventArgs(CurrentPage + 1));
            }
        }

        private void LastPage_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentPage < TotalPages)
            {
                PageChanged?.Invoke(this, new PageChangedEventArgs(TotalPages));
            }
        }

        private void PageButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int pageNumber)
            {
                PageChanged?.Invoke(this, new PageChangedEventArgs(pageNumber));
            }
        }

        private void PageSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PageSizeComboBox.SelectedItem is ComboBoxItem selectedItem &&
                int.TryParse(selectedItem.Content?.ToString(), out int newPageSize))
            {
                PageSizeChanged?.Invoke(this, new PageSizeChangedEventArgs(newPageSize));
            }
        }
    }

    // Event Args Classes
    public class PageChangedEventArgs : EventArgs
    {
        public int NewPage { get; }

        public PageChangedEventArgs(int newPage)
        {
            NewPage = newPage;
        }
    }

    public class PageSizeChangedEventArgs : EventArgs
    {
        public int NewPageSize { get; }

        public PageSizeChangedEventArgs(int newPageSize)
        {
            NewPageSize = newPageSize;
        }
    }
}
