using System.Collections.Generic;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Services
{
    public class OfferService
    {
        private readonly OfferRepository _repository;
        public OfferService(OfferRepository repository)
        {
            _repository = repository;
        }
        public IEnumerable<Offer> GetAllOffers() => _repository.GetAll();
        public Offer? GetOfferById(int id) => _repository.GetById(id);
        public void AddOffer(Offer offer)
        {
            _repository.Add(offer);
            if (!string.IsNullOrEmpty(offer.ScientificName))
            {
                _repository.AddScientificName(offer.ScientificName);
            }
        }
        public void UpdateOffer(Offer offer) => _repository.Update(offer);
        public void DeleteOffer(int id) => _repository.Delete(id);
        public IEnumerable<ScientificName> GetScientificNames() => _repository.GetScientificNames();
    }
} 