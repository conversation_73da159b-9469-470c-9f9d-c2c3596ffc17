using HR_InvoiceArchiver.Models;
using System;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة إدارة الإعدادات
    /// </summary>
    public interface ISettingsService
    {
        /// <summary>
        /// تحميل الإعدادات
        /// </summary>
        Task<SettingsModel> LoadSettingsAsync();

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        Task SaveSettingsAsync(SettingsModel settings);

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        Task ResetToDefaultsAsync();

        /// <summary>
        /// تصدير الإعدادات إلى ملف
        /// </summary>
        Task<string> ExportSettingsAsync(string? filePath = null);

        /// <summary>
        /// استيراد الإعدادات من ملف
        /// </summary>
        Task<bool> ImportSettingsAsync(string filePath);

        /// <summary>
        /// التحقق من صحة الإعدادات
        /// </summary>
        Task<(bool IsValid, string[] Errors)> ValidateSettingsAsync(SettingsModel settings);

        /// <summary>
        /// الحصول على إعداد محدد
        /// </summary>
        Task<T?> GetSettingAsync<T>(string key, T? defaultValue = default);

        /// <summary>
        /// تعيين إعداد محدد
        /// </summary>
        Task SetSettingAsync<T>(string key, T value);

        /// <summary>
        /// حذف إعداد محدد
        /// </summary>
        Task RemoveSettingAsync(string key);

        /// <summary>
        /// التحقق من وجود إعداد
        /// </summary>
        Task<bool> SettingExistsAsync(string key);

        /// <summary>
        /// إنشاء نسخة احتياطية من الإعدادات
        /// </summary>
        Task<string> CreateBackupAsync();

        /// <summary>
        /// استعادة الإعدادات من نسخة احتياطية
        /// </summary>
        Task<bool> RestoreFromBackupAsync(string backupPath);

        /// <summary>
        /// الحصول على معلومات النظام
        /// </summary>
        Task<SystemInfo> GetSystemInfoAsync();

        /// <summary>
        /// تطبيق الإعدادات على النظام
        /// </summary>
        Task ApplySettingsAsync(SettingsModel settings);

        /// <summary>
        /// مراقبة تغييرات الإعدادات
        /// </summary>
        event EventHandler<SettingsChangedEventArgs>? SettingsChanged;
    }

    /// <summary>
    /// معلومات النظام
    /// </summary>
    public class SystemInfo
    {
        public string ApplicationVersion { get; set; } = string.Empty;
        public string DatabaseVersion { get; set; } = string.Empty;
        public long DatabaseSize { get; set; }
        public string DatabasePath { get; set; } = string.Empty;
        public DateTime LastBackup { get; set; }
        public DateTime LastCloudSync { get; set; }
        public int TotalInvoices { get; set; }
        public int TotalPayments { get; set; }
        public int TotalSuppliers { get; set; }
        public long MemoryUsage { get; set; }
        public TimeSpan Uptime { get; set; }
        public string OperatingSystem { get; set; } = string.Empty;
        public string DotNetVersion { get; set; } = string.Empty;
    }

    /// <summary>
    /// معطيات حدث تغيير الإعدادات
    /// </summary>
    public class SettingsChangedEventArgs : EventArgs
    {
        public string SettingKey { get; set; } = string.Empty;
        public object? OldValue { get; set; }
        public object? NewValue { get; set; }
        public DateTime ChangedAt { get; set; } = DateTime.Now;
        public string? ChangedBy { get; set; }
    }
}
