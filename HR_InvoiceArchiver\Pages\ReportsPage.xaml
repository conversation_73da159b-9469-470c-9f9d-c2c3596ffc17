<UserControl x:Class="HR_InvoiceArchiver.Pages.ReportsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="White">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" 
                Background="#2196F3" 
                CornerRadius="10" 
                Padding="20" 
                Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="📊 التقارير والإحصائيات" 
                           FontSize="24" 
                           FontWeight="Bold" 
                           Foreground="White" 
                           HorizontalAlignment="Center"/>
                <TextBlock Text="عرض وتحليل البيانات المالية" 
                           FontSize="14" 
                           Foreground="White" 
                           HorizontalAlignment="Center" 
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Statistics Cards -->
                <TextBlock Text="الإحصائيات العامة" 
                           FontSize="18" 
                           FontWeight="Bold" 
                           Margin="0,0,0,15"/>
                
                <Grid Margin="0,0,0,30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Total Invoices Card -->
                    <Border Grid.Column="0" 
                            Background="#E3F2FD" 
                            CornerRadius="10" 
                            Padding="15" 
                            Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📄" FontSize="24" HorizontalAlignment="Center"/>
                            <TextBlock Text="إجمالي الفواتير" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock x:Name="TotalInvoicesText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Total Amount Card -->
                    <Border Grid.Column="1" 
                            Background="#E8F5E8" 
                            CornerRadius="10" 
                            Padding="15" 
                            Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center"/>
                            <TextBlock Text="إجمالي المبالغ" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock x:Name="TotalAmountText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#388E3C"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Total Payments Card -->
                    <Border Grid.Column="2" 
                            Background="#FFF3E0" 
                            CornerRadius="10" 
                            Padding="15" 
                            Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="💳" FontSize="24" HorizontalAlignment="Center"/>
                            <TextBlock Text="إجمالي المدفوعات" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock x:Name="TotalPaymentsText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#F57C00"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Outstanding Amount Card -->
                    <Border Grid.Column="3" 
                            Background="#FFEBEE" 
                            CornerRadius="10" 
                            Padding="15" 
                            Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Center"/>
                            <TextBlock Text="المبالغ المستحقة" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock x:Name="OutstandingAmountText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#D32F2F"/>
                        </StackPanel>
                    </Border>
                </Grid>
                
                <!-- Report Type Buttons -->
                <TextBlock Text="أنواع التقارير"
                           FontSize="20"
                           FontWeight="Bold"
                           Foreground="#2C3E50"
                           Margin="0,0,0,20"/>

                <Grid Margin="0,0,0,30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Invoices Report Button -->
                    <Border Grid.Column="0"
                            Background="White"
                            CornerRadius="15"
                            Margin="8"
                            BorderThickness="2"
                            BorderBrush="#E3F2FD">
                        <Border.Effect>
                            <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                        </Border.Effect>
                        <Button x:Name="InvoicesReportButton"
                                Background="Transparent"
                                BorderThickness="0"
                                Padding="15,20"
                                Cursor="Hand"
                                Click="InvoicesReportButton_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="15">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                      VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background">
                                                            <Setter.Value>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                    <GradientStop Color="#2196F3" Offset="0"/>
                                                                    <GradientStop Color="#1976D2" Offset="1"/>
                                                                </LinearGradientBrush>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="📄" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock Text="تقرير الفواتير"
                                           FontSize="14"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           Foreground="#2196F3"/>
                                <TextBlock Text="عرض جميع الفواتير"
                                           FontSize="11"
                                           HorizontalAlignment="Center"
                                           Foreground="#757575"
                                           Margin="0,3,0,0"/>
                            </StackPanel>
                        </Button>
                    </Border>

                    <!-- Payments Report Button -->
                    <Border Grid.Column="1"
                            Background="White"
                            CornerRadius="15"
                            Margin="8"
                            BorderThickness="2"
                            BorderBrush="#E8F5E8">
                        <Border.Effect>
                            <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                        </Border.Effect>
                        <Button x:Name="PaymentsReportButton"
                                Background="Transparent"
                                BorderThickness="0"
                                Padding="15,20"
                                Cursor="Hand"
                                Click="PaymentsReportButton_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="15">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                      VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background">
                                                            <Setter.Value>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                    <GradientStop Color="#4CAF50" Offset="0"/>
                                                                    <GradientStop Color="#388E3C" Offset="1"/>
                                                                </LinearGradientBrush>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="💳" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock Text="تقرير المدفوعات"
                                           FontSize="14"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           Foreground="#4CAF50"/>
                                <TextBlock Text="عرض جميع المدفوعات"
                                           FontSize="11"
                                           HorizontalAlignment="Center"
                                           Foreground="#757575"
                                           Margin="0,3,0,0"/>
                            </StackPanel>
                        </Button>
                    </Border>

                    <!-- Suppliers Report Button -->
                    <Border Grid.Column="2"
                            Background="White"
                            CornerRadius="15"
                            Margin="8"
                            BorderThickness="2"
                            BorderBrush="#FFF3E0">
                        <Border.Effect>
                            <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                        </Border.Effect>
                        <Button x:Name="SuppliersReportButton"
                                Background="Transparent"
                                BorderThickness="0"
                                Padding="15,20"
                                Cursor="Hand"
                                Click="SuppliersReportButton_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="15">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                      VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background">
                                                            <Setter.Value>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                    <GradientStop Color="#FF9800" Offset="0"/>
                                                                    <GradientStop Color="#F57C00" Offset="1"/>
                                                                </LinearGradientBrush>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="🏢" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock Text="تقرير الموردين"
                                           FontSize="14"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           Foreground="#FF9800"/>
                                <TextBlock Text="إحصائيات الموردين"
                                           FontSize="11"
                                           HorizontalAlignment="Center"
                                           Foreground="#757575"
                                           Margin="0,3,0,0"/>
                            </StackPanel>
                        </Button>
                    </Border>

                    <!-- Monthly Report Button -->
                    <Border Grid.Column="3"
                            Background="White"
                            CornerRadius="15"
                            Margin="8"
                            BorderThickness="2"
                            BorderBrush="#F3E5F5">
                        <Border.Effect>
                            <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                        </Border.Effect>
                        <Button x:Name="MonthlyReportButton"
                                Background="Transparent"
                                BorderThickness="0"
                                Padding="15,20"
                                Cursor="Hand"
                                Click="MonthlyReportButton_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="15">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                      VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background">
                                                            <Setter.Value>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                    <GradientStop Color="#9C27B0" Offset="0"/>
                                                                    <GradientStop Color="#7B1FA2" Offset="1"/>
                                                                </LinearGradientBrush>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock Text="التقرير الشهري"
                                           FontSize="14"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           Foreground="#9C27B0"/>
                                <TextBlock Text="تحليل شهري مفصل"
                                           FontSize="11"
                                           HorizontalAlignment="Center"
                                           Foreground="#757575"
                                           Margin="0,3,0,0"/>
                            </StackPanel>
                        </Button>
                    </Border>
                </Grid>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,30">
                    <!-- Load Data Button -->
                    <Border Background="White"
                            CornerRadius="25"
                            Margin="10"
                            BorderThickness="2"
                            BorderBrush="#E0E0E0">
                        <Border.Effect>
                            <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                        </Border.Effect>
                        <Button x:Name="LoadDataButton"
                                Background="Transparent"
                                BorderThickness="0"
                                Padding="20,12"
                                Cursor="Hand"
                                Click="LoadDataButton_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="25">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                      VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background">
                                                            <Setter.Value>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                    <GradientStop Color="#607D8B" Offset="0"/>
                                                                    <GradientStop Color="#455A64" Offset="1"/>
                                                                </LinearGradientBrush>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔄" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="تحميل البيانات"
                                           FontSize="14"
                                           FontWeight="Bold"
                                           VerticalAlignment="Center"
                                           Foreground="#607D8B"/>
                            </StackPanel>
                        </Button>
                    </Border>

                    <!-- Export Button -->
                    <Border Background="White"
                            CornerRadius="25"
                            Margin="10"
                            BorderThickness="2"
                            BorderBrush="#E0E0E0">
                        <Border.Effect>
                            <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                        </Border.Effect>
                        <Button x:Name="ExportButton"
                                Background="Transparent"
                                BorderThickness="0"
                                Padding="20,12"
                                Cursor="Hand"
                                Click="ExportButton_Click"
                                IsEnabled="False">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="25">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                      VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background">
                                                            <Setter.Value>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                    <GradientStop Color="#795548" Offset="0"/>
                                                                    <GradientStop Color="#5D4037" Offset="1"/>
                                                                </LinearGradientBrush>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger>
                                                    <Trigger Property="IsEnabled" Value="False">
                                                        <Setter Property="Opacity" Value="0.5"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📤" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="تصدير التقرير"
                                           FontSize="14"
                                           FontWeight="Bold"
                                           VerticalAlignment="Center"
                                           Foreground="#795548"/>
                            </StackPanel>
                        </Button>
                    </Border>
                </StackPanel>
                
                <!-- Report Display Area -->
                <Border Background="#F8F9FA"
                        CornerRadius="10"
                        Padding="20"
                        Margin="0,20,0,0"
                        MinHeight="300">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Report Header -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                            <TextBlock x:Name="ReportTitleText"
                                       Text="اختر نوع التقرير"
                                       FontSize="20"
                                       FontWeight="Bold"
                                       Foreground="#2196F3"/>
                            <TextBlock x:Name="ReportDateText"
                                       Text="استخدم الأزرار أعلاه لعرض التقارير المختلفة"
                                       FontSize="12"
                                       Foreground="Gray"
                                       Margin="20,5,0,0"/>
                        </StackPanel>

                        <!-- Search Box -->
                        <Border Grid.Row="1"
                                Background="White"
                                CornerRadius="25"
                                Padding="15,10"
                                Margin="0,0,0,15"
                                BorderBrush="#E0E0E0"
                                BorderThickness="1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0"
                                           Text="🔍"
                                           FontSize="16"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>

                                <TextBox x:Name="SearchTextBox"
                                         Grid.Column="1"
                                         Background="Transparent"
                                         BorderThickness="0"
                                         FontSize="14"
                                         VerticalAlignment="Center"
                                         TextChanged="SearchTextBox_TextChanged"/>

                                <TextBlock Grid.Column="1"
                                           Text="البحث في التقرير..."
                                           FontSize="14"
                                           Foreground="Gray"
                                           VerticalAlignment="Center"
                                           IsHitTestVisible="False">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Text, ElementName=SearchTextBox}" Value="">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>

                                <Button x:Name="ClearSearchButton"
                                        Grid.Column="2"
                                        Content="✕"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Foreground="Gray"
                                        FontSize="12"
                                        Padding="5"
                                        Click="ClearSearchButton_Click"
                                        Visibility="Collapsed"/>
                            </Grid>
                        </Border>

                        <!-- Data Grid -->
                        <DataGrid x:Name="ReportDataGrid"
                                  Grid.Row="2"
                                  Background="White"
                                  BorderThickness="0"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  AlternatingRowBackground="#F8F9FA"
                                  RowBackground="White"
                                  FontSize="12"
                                  FlowDirection="RightToLeft">
                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#2196F3"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="Padding" Value="10,8"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                </Style>
                            </DataGrid.ColumnHeaderStyle>
                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Height" Value="35"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.RowStyle>
                        </DataGrid>

                        <!-- Empty State -->
                        <StackPanel x:Name="EmptyStatePanel"
                                    Grid.Row="2"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Visibility="Visible">
                            <TextBlock Text="📊" FontSize="48" HorizontalAlignment="Center" Opacity="0.5"/>
                            <TextBlock x:Name="StatusText"
                                       Text="اضغط على 'تحميل البيانات' لعرض الإحصائيات"
                                       FontSize="14"
                                       HorizontalAlignment="Center"
                                       Foreground="Gray"
                                       Margin="0,10,0,0"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
