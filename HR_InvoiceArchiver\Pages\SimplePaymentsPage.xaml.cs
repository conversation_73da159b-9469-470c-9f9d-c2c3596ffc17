using System;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class SimplePaymentsPage : UserControl, INavigationAware
    {
        private readonly IToastService _toastService;

        public SimplePaymentsPage(IToastService toastService)
        {
            System.Console.WriteLine("SimplePaymentsPage: Constructor started");
            InitializeComponent();
            _toastService = toastService;
            System.Console.WriteLine("SimplePaymentsPage: Constructor completed");
        }

        public void OnNavigatedTo(object parameter)
        {
            System.Console.WriteLine("SimplePaymentsPage: OnNavigatedTo called");
            _toastService?.ShowSuccess("نجح!", "تم التنقل إلى صفحة المدفوعات بنجاح");
        }

        public void OnNavigatedFrom()
        {
            System.Console.WriteLine("SimplePaymentsPage: OnNavigatedFrom called");
        }

        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("اختبار", "تم النقر على الزر بنجاح!");
        }
    }
}
