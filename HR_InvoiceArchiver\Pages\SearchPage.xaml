<UserControl x:Class="HR_InvoiceArchiver.Pages.SearchPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="#F5F6FA">

    <UserControl.Resources>
        <!-- Modern Button Styles -->
        <!-- حذف جميع الأنماط المحلية للأزرار والبطاقات هنا -->
    </UserControl.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Modern Header -->
        <materialDesign:Card Grid.Row="0" Padding="24" Margin="0,0,0,20" materialDesign:ElevationAssist.Elevation="Dp4">
            <materialDesign:Card.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#007BFF" Offset="0"/>
                    <GradientStop Color="#0056B3" Offset="1"/>
                </LinearGradientBrush>
            </materialDesign:Card.Background>
            <materialDesign:Card.Effect>
                <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="20" ShadowDepth="8"/>
            </materialDesign:Card.Effect>
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                    <materialDesign:PackIcon Kind="Magnify" Width="32" Height="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="البحث المتقدم"
                             FontSize="28" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                <TextBlock Text="ابحث في الفواتير والموردين والمدفوعات باستخدام معايير متعددة ومتقدمة"
                         FontSize="16" Foreground="#E3F2FD" Opacity="0.9"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Enhanced Search Section -->
        <materialDesign:Card Grid.Row="1" Padding="24" Margin="0,0,0,20" materialDesign:ElevationAssist.Elevation="Dp4" Background="White">
            <materialDesign:Card.Effect>
                <DropShadowEffect Color="Gray" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
            </materialDesign:Card.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Search Title with Icon -->
                <StackPanel Grid.Row="0" Grid.ColumnSpan="3" Orientation="Horizontal" Margin="0,0,0,20">
                    <materialDesign:PackIcon Kind="FilterVariant" Width="24" Height="24"
                                           Foreground="#007BFF" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Text="فلاتر البحث المتقدم"
                             FontSize="20" FontWeight="SemiBold" Foreground="#333" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Search Fields Row 1 -->
                <materialDesign:Card Grid.Row="1" Grid.Column="0" Padding="4" Margin="0,0,8,8"
                                   materialDesign:ElevationAssist.Elevation="Dp1" Background="White">
                    <TextBox x:Name="InvoiceNumberTextBox"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="رقم الفاتورة"
                           materialDesign:TextFieldAssist.HasLeadingIcon="True"
                           materialDesign:TextFieldAssist.LeadingIcon="FileDocument"/>
                </materialDesign:Card>

                <materialDesign:Card Grid.Row="1" Grid.Column="1" Padding="4" Margin="8,0,8,8"
                                   materialDesign:ElevationAssist.Elevation="Dp1" Background="White">
                    <ComboBox x:Name="SupplierComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            materialDesign:HintAssist.Hint="اختر المورد"
                            DisplayMemberPath="Name"
                            SelectedValuePath="Id"
                            materialDesign:TextFieldAssist.HasLeadingIcon="True"
                            materialDesign:TextFieldAssist.LeadingIcon="Account"/>
                </materialDesign:Card>

                <materialDesign:Card Grid.Row="1" Grid.Column="2" Padding="4" Margin="8,0,0,8"
                                   materialDesign:ElevationAssist.Elevation="Dp1" Background="White">
                    <ComboBox x:Name="StatusComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            materialDesign:HintAssist.Hint="حالة الدفع"
                            materialDesign:TextFieldAssist.HasLeadingIcon="True"
                            materialDesign:TextFieldAssist.LeadingIcon="CheckCircle">
                        <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                        <ComboBoxItem Content="مدفوعة"/>
                        <ComboBoxItem Content="غير مدفوعة"/>
                        <ComboBoxItem Content="مدفوعة جزئياً"/>
                    </ComboBox>
                </materialDesign:Card>

                <!-- Search Fields Row 2 -->
                <materialDesign:Card Grid.Row="2" Grid.Column="0" Padding="4" Margin="0,0,8,16"
                                   materialDesign:ElevationAssist.Elevation="Dp1" Background="White">
                    <DatePicker x:Name="FromDatePicker"
                              Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                              materialDesign:HintAssist.Hint="من تاريخ"
                              materialDesign:TextFieldAssist.HasLeadingIcon="True"
                              materialDesign:TextFieldAssist.LeadingIcon="CalendarStart"/>
                </materialDesign:Card>

                <materialDesign:Card Grid.Row="2" Grid.Column="1" Padding="4" Margin="8,0,8,16"
                                   materialDesign:ElevationAssist.Elevation="Dp1" Background="White">
                    <DatePicker x:Name="ToDatePicker"
                              Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                              materialDesign:HintAssist.Hint="إلى تاريخ"
                              materialDesign:TextFieldAssist.HasLeadingIcon="True"
                              materialDesign:TextFieldAssist.LeadingIcon="CalendarEnd"/>
                </materialDesign:Card>

                <materialDesign:Card Grid.Row="2" Grid.Column="2" Padding="4" Margin="8,0,0,16"
                                   materialDesign:ElevationAssist.Elevation="Dp1" Background="White">
                    <TextBox x:Name="AmountTextBox"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="المبلغ (IQD)"
                           materialDesign:TextFieldAssist.HasLeadingIcon="True"
                           materialDesign:TextFieldAssist.LeadingIcon="CurrencyUsd"/>
                </materialDesign:Card>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="3" Grid.ColumnSpan="3" Orientation="Horizontal"
                          HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button x:Name="SearchButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Click="SearchButton_Click"
                          Margin="0,0,16,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Magnify" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="بحث متقدم"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ClearButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Click="ClearButton_Click"
                          Margin="0,0,16,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="مسح الفلاتر"/>
                        </StackPanel>
                    </Button>


                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Statistics Cards Section -->
        <Grid Grid.Row="2" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Results Card -->
            <materialDesign:Card Grid.Column="0" Padding="20" Margin="0,0,8,0" materialDesign:ElevationAssist.Elevation="Dp4">
                <materialDesign:Card.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#007BFF" Offset="0"/>
                        <GradientStop Color="#0056B3" Offset="1"/>
                    </LinearGradientBrush>
                </materialDesign:Card.Background>
                <materialDesign:Card.Effect>
                    <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
                </materialDesign:Card.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <materialDesign:PackIcon Kind="FileDocument" Width="24" Height="24" Foreground="White"/>
                        <TextBlock Text="إجمالي النتائج" FontSize="14" FontWeight="Medium" Foreground="White" Margin="8,0,0,0"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalResultsText" Text="0" FontSize="28" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="نتيجة بحث" FontSize="12" Foreground="#E3F2FD" Opacity="0.8"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Total Amount Card -->
            <materialDesign:Card Grid.Column="1" Padding="20" Margin="8,0,8,0" materialDesign:ElevationAssist.Elevation="Dp4">
                <materialDesign:Card.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#28A745" Offset="0"/>
                        <GradientStop Color="#1E7E34" Offset="1"/>
                    </LinearGradientBrush>
                </materialDesign:Card.Background>
                <materialDesign:Card.Effect>
                    <DropShadowEffect Color="#28A745" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
                </materialDesign:Card.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <materialDesign:PackIcon Kind="CurrencyUsd" Width="24" Height="24" Foreground="White"/>
                        <TextBlock Text="إجمالي المبلغ" FontSize="14" FontWeight="Medium" Foreground="White" Margin="8,0,0,0"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalAmountText" Text="0" FontSize="28" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#D4EDDA" Opacity="0.8"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Paid Amount Card -->
            <materialDesign:Card Grid.Column="2" Padding="20" Margin="8,0,8,0" materialDesign:ElevationAssist.Elevation="Dp4">
                <materialDesign:Card.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FD7E14" Offset="0"/>
                        <GradientStop Color="#E55A00" Offset="1"/>
                    </LinearGradientBrush>
                </materialDesign:Card.Background>
                <materialDesign:Card.Effect>
                    <DropShadowEffect Color="#FD7E14" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
                </materialDesign:Card.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <materialDesign:PackIcon Kind="CheckCircle" Width="24" Height="24" Foreground="White"/>
                        <TextBlock Text="المبلغ المدفوع" FontSize="14" FontWeight="Medium" Foreground="White" Margin="8,0,0,0"/>
                    </StackPanel>
                    <TextBlock x:Name="PaidAmountText" Text="0" FontSize="28" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#FFF3CD" Opacity="0.8"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Outstanding Amount Card -->
            <materialDesign:Card Grid.Column="3" Padding="20" Margin="8,0,0,0" materialDesign:ElevationAssist.Elevation="Dp4">
                <materialDesign:Card.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#DC3545" Offset="0"/>
                        <GradientStop Color="#B02A37" Offset="1"/>
                    </LinearGradientBrush>
                </materialDesign:Card.Background>
                <materialDesign:Card.Effect>
                    <DropShadowEffect Color="#DC3545" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
                </materialDesign:Card.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <materialDesign:PackIcon Kind="AlertCircle" Width="24" Height="24" Foreground="White"/>
                        <TextBlock Text="المبلغ المتبقي" FontSize="14" FontWeight="Medium" Foreground="White" Margin="8,0,0,0"/>
                    </StackPanel>
                    <TextBlock x:Name="OutstandingAmountText" Text="0" FontSize="28" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#F8D7DA" Opacity="0.8"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Enhanced Results Section -->
        <materialDesign:Card Grid.Row="3" materialDesign:ElevationAssist.Elevation="Dp4" Background="White">
            <materialDesign:Card.Effect>
                <DropShadowEffect Color="Gray" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
            </materialDesign:Card.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Modern Results Header -->
                <materialDesign:Card Grid.Row="0" Padding="20" materialDesign:ElevationAssist.Elevation="Dp2">
                    <materialDesign:Card.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                            <GradientStop Color="#007BFF" Offset="0"/>
                            <GradientStop Color="#0056B3" Offset="1"/>
                        </LinearGradientBrush>
                    </materialDesign:Card.Background>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="TableLarge" Width="28" Height="28" Foreground="White" VerticalAlignment="Center"/>
                        <TextBlock Text="نتائج البحث المتقدم"
                                 FontSize="20" FontWeight="Bold" Foreground="White"
                                 VerticalAlignment="Center" Margin="12,0,0,0"/>
                        <Border Background="White" CornerRadius="12" Padding="8,4" Margin="16,0,0,0">
                            <TextBlock x:Name="ResultsCountText" Text="0 نتيجة"
                                     FontSize="14" FontWeight="SemiBold" Foreground="#007BFF"/>
                        </Border>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Enhanced Results Grid -->
                <Grid Grid.Row="1" Margin="20">
                    <!-- Loading Panel -->
                    <materialDesign:Card x:Name="LoadingPanel" Visibility="Collapsed"
                                       Padding="40" HorizontalAlignment="Center" VerticalAlignment="Center"
                                       materialDesign:ElevationAssist.Elevation="Dp4" Background="White">
                        <StackPanel HorizontalAlignment="Center">
                            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       Value="0" IsIndeterminate="True" Width="50" Height="50" Margin="0,0,0,16"/>
                            <TextBlock Text="جاري البحث..." FontSize="16" FontWeight="Medium"
                                     Foreground="#666" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Empty State Panel -->
                    <materialDesign:Card x:Name="EmptyStatePanel" Visibility="Collapsed"
                                       Padding="40" HorizontalAlignment="Center" VerticalAlignment="Center"
                                       materialDesign:ElevationAssist.Elevation="Dp2" Background="#F8F9FA">
                        <StackPanel HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="FileSearchOutline" Width="64" Height="64"
                                                   Foreground="#CCC" Margin="0,0,0,16"/>
                            <TextBlock Text="لا توجد نتائج للبحث" FontSize="18" FontWeight="Medium"
                                     Foreground="#666" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                            <TextBlock Text="جرب تعديل معايير البحث أو مسح الفلاتر" FontSize="14"
                                     Foreground="#999" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Data Grid -->
                    <DataGrid x:Name="ResultsDataGrid"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            SelectionMode="Single"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="Column"
                            materialDesign:DataGridAssist.CellPadding="12"
                            materialDesign:DataGridAssist.ColumnHeaderPadding="12"
                            Background="White"
                            RowBackground="White"
                            AlternatingRowBackground="#F8F9FA"
                            FontSize="14">

                        <DataGrid.ColumnHeaderStyle>
                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                <Setter Property="Background" Value="#007BFF"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Height" Value="45"/>
                            </Style>
                        </DataGrid.ColumnHeaderStyle>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                                <Setter Property="Height" Value="50"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="130">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#007BFF"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="160">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Medium"/>
                                        <Setter Property="Foreground" Value="#333"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=dd/MM/yyyy}" Width="110">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#666"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المبلغ (IQD)" Binding="{Binding Amount, StringFormat=N0}" Width="130">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#28A745"/>
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المدفوع (IQD)" Binding="{Binding PaidAmount, StringFormat=N0}" Width="130">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#FD7E14"/>
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="الحالة" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="مدفوع">
                                                            <Setter Property="Background" Value="#D4EDDA"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="غير مدفوع">
                                                            <Setter Property="Background" Value="#F8D7DA"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="مدفوع جزئياً">
                                                            <Setter Property="Background" Value="#FFF3CD"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding Status}" FontSize="12" FontWeight="SemiBold">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="مدفوع">
                                                                <Setter Property="Foreground" Value="#155724"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="غير مدفوع">
                                                                <Setter Property="Foreground" Value="#721C24"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="مدفوع جزئياً">
                                                                <Setter Property="Foreground" Value="#856404"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#666"/>
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>

                <!-- Enhanced Action Panel -->
                <materialDesign:Card Grid.Row="2" Padding="20" Background="#F8F9FA"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Results Info -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="Information" Width="20" Height="20"
                                                   Foreground="#007BFF" VerticalAlignment="Center"/>
                            <TextBlock x:Name="ResultsInfoText" Text="اختر معايير البحث وانقر على 'بحث متقدم'"
                                     FontSize="14" Foreground="#666" Margin="8,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- Export Buttons -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button x:Name="ExportExcelButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  Click="ExportExcelButton_Click"
                                  Margin="0,0,12,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileExcel" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="تصدير Excel"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="ExportPdfButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Click="ExportPdfButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FilePdfBox" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="تصدير PDF"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>

            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
