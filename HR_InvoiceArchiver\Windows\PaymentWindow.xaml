<Window x:Class="HR_InvoiceArchiver.Windows.PaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
        Title="إدارة المدفوعات والوصولات"
        Height="800" Width="1400"
        MinHeight="700" MinWidth="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <converters:StatusToTextConverter x:Key="StatusToTextConverter"/>
        <converters:StatusToColorConverter x:Key="StatusToColorConverter"/>
        <converters:CurrencyConverter x:Key="CurrencyConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Card Style -->
        <Style x:Key="PaymentCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="16,8"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        </Style>

        <!-- DataGrid Style -->
        <Style x:Key="PaymentDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="materialDesign:DataGridAssist.CellPadding" Value="8"/>
            <Setter Property="materialDesign:DataGridAssist.ColumnHeaderPadding" Value="8"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- App Bar -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <materialDesign:PackIcon Grid.Column="0" Kind="CreditCard" Width="24" Height="24"
                                       VerticalAlignment="Center" Margin="0,0,12,0"/>

                <TextBlock Grid.Column="1" Text="إدارة المدفوعات والوصولات"
                          FontSize="18" FontWeight="Medium" VerticalAlignment="Center"/>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Click="AddPaymentButton_Click" Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,4,0"/>
                            <TextBlock Text="إضافة مدفوعة"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Click="RefreshButton_Click" ToolTip="تحديث">
                        <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="16"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Payments List -->
            <materialDesign:Card Grid.Column="0" Style="{StaticResource PaymentCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <Grid Grid.Row="0" Margin="0,0,0,16">
                        <TextBlock Text="قائمة المدفوعات" FontSize="18" FontWeight="Medium"
                                 HorizontalAlignment="Right"/>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                            <TextBox x:Name="SearchTextBox" Width="250" Margin="0,0,8,0"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="البحث في المدفوعات..."
                                   Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}"/>
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                  Click="SearchButton_Click" ToolTip="بحث">
                                <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20"/>
                            </Button>
                        </StackPanel>
                    </Grid>

                    <!-- Filters -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,16">
                        <ComboBox x:Name="FilterStatusComboBox" Width="150" Margin="0,0,8,0"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                materialDesign:HintAssist.Hint="تصفية حسب الحالة"
                                SelectionChanged="FilterStatusComboBox_SelectionChanged"/>

                        <DatePicker x:Name="FilterDatePicker" Width="150" Margin="0,0,8,0"
                                  Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                  materialDesign:HintAssist.Hint="تصفية حسب التاريخ"
                                  SelectedDateChanged="FilterDatePicker_SelectedDateChanged"/>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                              Click="ClearFiltersButton_Click" Content="مسح التصفية"/>
                    </StackPanel>

                    <!-- DataGrid -->
                    <DataGrid Grid.Row="2" x:Name="PaymentsDataGrid"
                            Style="{StaticResource PaymentDataGridStyle}"
                            ItemsSource="{Binding FilteredPayments}"
                            SelectedItem="{Binding SelectedPayment}"
                            SelectionChanged="PaymentsDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الوصل" Binding="{Binding ReceiptNumber}" Width="120"/>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding Invoice.InvoiceNumber}" Width="120"/>
                            <DataGridTextColumn Header="المورد" Binding="{Binding Invoice.Supplier.Name}" Width="150"/>
                            <DataGridTextColumn Header="تاريخ الدفع" Binding="{Binding PaymentDate, StringFormat='yyyy/MM/dd'}" Width="100"/>
                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, Converter={StaticResource CurrencyConverter}}" Width="120"/>
                            <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="100"/>
                            <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="*" MaxWidth="200"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Payment Details Panel -->
            <materialDesign:Card Grid.Column="2" Style="{StaticResource PaymentCardStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="PaymentDetailsPanel">
                        <TextBlock Text="تفاصيل المدفوعة" FontSize="18" FontWeight="Medium"
                                 HorizontalAlignment="Center" Margin="0,0,0,24"/>

                        <!-- Selected Payment Info -->
                        <Border x:Name="SelectedPaymentPanel"
                              Background="{DynamicResource MaterialDesignCardBackground}"
                              CornerRadius="8" Padding="16" Margin="0,0,0,16"
                              Visibility="{Binding HasSelectedPayment, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock Text="معلومات المدفوعة المحددة" FontWeight="Medium"
                                         Margin="0,0,0,12" FontSize="14"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الوصل:" FontWeight="Medium" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedPayment.ReceiptNumber}" Margin="0,0,0,4"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="رقم الفاتورة:" FontWeight="Medium" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedPayment.Invoice.InvoiceNumber}" Margin="0,0,0,4"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="المورد:" FontWeight="Medium" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedPayment.Invoice.Supplier.Name}" Margin="0,0,0,4"/>

                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ:" FontWeight="Medium" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SelectedPayment.Amount, Converter={StaticResource CurrencyConverter}}" Margin="0,0,0,4"/>

                                    <TextBlock Grid.Row="4" Grid.Column="0" Text="تاريخ الدفع:" FontWeight="Medium" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding SelectedPayment.PaymentDate, StringFormat='yyyy/MM/dd'}" Margin="0,0,0,4"/>

                                    <TextBlock Grid.Row="5" Grid.Column="0" Text="طريقة الدفع:" FontWeight="Medium" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding SelectedPayment.PaymentMethod}" Margin="0,0,0,4"/>
                                </Grid>

                                <TextBlock Text="الملاحظات:" FontWeight="Medium" Margin="0,8,0,4"/>
                                <TextBlock Text="{Binding SelectedPayment.Notes}" TextWrapping="Wrap"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </StackPanel>
                        </Border>

                        <!-- Action Buttons -->
                        <StackPanel Orientation="Vertical">
                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                  Click="EditPaymentButton_Click"
                                  IsEnabled="{Binding HasSelectedPayment}"
                                  Margin="0,0,0,8">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Edit" Width="16" Height="16" Margin="0,0,4,0"/>
                                    <TextBlock Text="تعديل المدفوعة"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Click="DeletePaymentButton_Click"
                                  IsEnabled="{Binding HasSelectedPayment}"
                                  Margin="0,0,0,8">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Margin="0,0,4,0"/>
                                    <TextBlock Text="حذف المدفوعة"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Click="ViewReceiptButton_Click"
                                  IsEnabled="{Binding HasSelectedPayment}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16" Margin="0,0,4,0"/>
                                    <TextBlock Text="عرض الوصل"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>
    </Grid>
</Window>
