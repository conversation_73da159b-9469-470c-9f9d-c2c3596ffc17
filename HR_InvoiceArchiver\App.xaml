<Application x:Class="HR_InvoiceArchiver.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:HR_InvoiceArchiver"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design Theme - v5.x correct syntax -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign2.Defaults.xaml" />
                <!-- Arabic Font Styles -->
                <ResourceDictionary Source="Styles/ArabicFontStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Arabic Font Settings -->
            <Style TargetType="{x:Type TextBlock}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS, Microsoft Sans Serif"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
                <Setter Property="TextOptions.TextHintingMode" Value="Fixed"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
                <Setter Property="SnapsToDevicePixels" Value="True"/>
                <Setter Property="FontSize" Value="12"/>
            </Style>

            <Style TargetType="{x:Type TextBox}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
                <Setter Property="SnapsToDevicePixels" Value="True"/>
            </Style>

            <Style TargetType="{x:Type Label}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
                <Setter Property="SnapsToDevicePixels" Value="True"/>
            </Style>

            <Style TargetType="{x:Type Button}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
                <Setter Property="SnapsToDevicePixels" Value="True"/>
            </Style>

            <Style TargetType="{x:Type Window}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
                <Setter Property="SnapsToDevicePixels" Value="True"/>
            </Style>

            <Style TargetType="{x:Type UserControl}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
                <Setter Property="SnapsToDevicePixels" Value="True"/>
            </Style>

            <Style TargetType="{x:Type Page}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
                <Setter Property="SnapsToDevicePixels" Value="True"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
