using System;
using System.Globalization;
using System.Windows.Data;

namespace HR_InvoiceArchiver.Converters
{
    public class CurrencyConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal amount)
            {
                // Format as whole number (no decimals) with thousand separators
                return $"{amount:N0} د.ع";
            }
            
            if (value is double doubleAmount)
            {
                return $"{doubleAmount:N0} د.ع";
            }
            
            if (value is int intAmount)
            {
                return $"{intAmount:N0} د.ع";
            }
            
            return "0 د.ع";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string text)
            {
                // Remove currency symbol and parse
                var cleanText = text.Replace("د.ع", "").Replace(",", "").Trim();
                if (decimal.TryParse(cleanText, out decimal result))
                {
                    return result;
                }
            }
            return 0m;
        }
    }
}
