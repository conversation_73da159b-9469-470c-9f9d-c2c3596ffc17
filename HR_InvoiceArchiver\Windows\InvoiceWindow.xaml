<Window x:Class="HR_InvoiceArchiver.Windows.InvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:HR_InvoiceArchiver.Windows"
        xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
        xmlns:controls="clr-namespace:HR_InvoiceArchiver.Controls"
        mc:Ignorable="d"
        Title="إدارة الفواتير"
        Height="800" Width="1200"
        MinHeight="700" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <!-- Status Color Converters -->
        <converters:StatusToColorConverter x:Key="StatusToColorConverter"/>
        <converters:StatusToTextConverter x:Key="StatusToTextConverter"/>
        <converters:CurrencyConverter x:Key="CurrencyConverter"/>

        <!-- DataGrid Column Header Style -->
        <Style x:Key="DataGridColumnHeaderStyle" TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
        </Style>

        <!-- DataGrid Row Style -->
        <Style x:Key="DataGridRowStyle" TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
            <Setter Property="Height" Value="48"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F5F5F5"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <!-- Main Content -->
    <Grid x:Name="MainContentGrid">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- App Bar -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" materialDesign:ElevationAssist.Elevation="Dp4" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Receipt" Width="24" Height="24" VerticalAlignment="Center" Foreground="White" Margin="0,0,8,0"/>
                    <TextBlock Text="إدارة الفواتير" FontSize="20" FontWeight="Bold" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconForegroundButton}"
                            ToolTip="تحديث" Click="RefreshButton_Click" Margin="4">
                        <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                    </Button>
                    <Button x:Name="ExportButton" Style="{StaticResource MaterialDesignIconForegroundButton}"
                            ToolTip="تصدير" Click="ExportButton_Click" Margin="4">
                        <materialDesign:PackIcon Kind="Export" Width="20" Height="20"/>
                    </Button>
                    <Button x:Name="SettingsButton" Style="{StaticResource MaterialDesignIconForegroundButton}"
                            ToolTip="الإعدادات" Click="SettingsButton_Click" Margin="4">
                        <materialDesign:PackIcon Kind="Settings" Width="20" Height="20"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Search and Filter Bar -->
        <materialDesign:Card Grid.Row="1" Margin="16,8" materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox x:Name="SearchTextBox" Grid.Column="0"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         materialDesign:HintAssist.Hint="البحث في الفواتير..."
                         materialDesign:HintAssist.IsFloating="True"
                         materialDesign:TextFieldAssist.HasLeadingIcon="True"
                         materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                         TextChanged="SearchTextBox_TextChanged"
                         Margin="0,0,8,0"/>

                <!-- Status Filter -->
                <ComboBox x:Name="StatusFilterComboBox" Grid.Column="1"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          materialDesign:HintAssist.Hint="الحالة"
                          materialDesign:HintAssist.IsFloating="True"
                          Width="120" Margin="4,0"
                          SelectionChanged="StatusFilterComboBox_SelectionChanged"/>

                <!-- Date Filter -->
                <DatePicker x:Name="FromDatePicker" Grid.Column="2"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            materialDesign:HintAssist.Hint="من تاريخ"
                            materialDesign:HintAssist.IsFloating="True"
                            Width="120" Margin="4,0"
                            SelectedDateChanged="DateFilter_Changed"/>

                <DatePicker x:Name="ToDatePicker" Grid.Column="3"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            materialDesign:HintAssist.Hint="إلى تاريخ"
                            materialDesign:HintAssist.IsFloating="True"
                            Width="120" Margin="4,0"
                            SelectedDateChanged="DateFilter_Changed"/>

                <!-- Clear Filter Button -->
                <Button x:Name="ClearFilterButton" Grid.Column="4"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Content="مسح الفلتر" Click="ClearFilterButton_Click" Margin="8,0,0,0"/>
            </Grid>
        </materialDesign:Card>

        <!-- Main DataGrid -->
        <materialDesign:Card Grid.Row="2" Margin="16,8" materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- DataGrid Header -->
                <Border Grid.Row="0" Background="#F5F5F5" Padding="16,12">
                    <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="قائمة الفواتير" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="AddInvoiceButton" Style="{StaticResource MaterialDesignRaisedButton}"
                                Content="إضافة فاتورة" Click="AddInvoiceButton_Click" Margin="0,0,8,0">
                            <Button.CommandParameter>
                                <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"/>
                            </Button.CommandParameter>
                        </Button>
                        <Button x:Name="EditInvoiceButton" Style="{StaticResource MaterialDesignOutlinedButton}"
                                Content="تعديل" Click="EditInvoiceButton_Click" Margin="0,0,8,0" IsEnabled="False">
                            <Button.CommandParameter>
                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                            </Button.CommandParameter>
                        </Button>
                        <Button x:Name="DeleteInvoiceButton" Style="{StaticResource MaterialDesignOutlinedButton}"
                                Content="حذف" Click="DeleteInvoiceButton_Click" IsEnabled="False">
                            <Button.CommandParameter>
                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                            </Button.CommandParameter>
                        </Button>
                    </StackPanel>
                    </Grid>
                </Border>

                <!-- DataGrid -->
                <DataGrid x:Name="InvoicesDataGrid" Grid.Row="1"
                          Style="{StaticResource MaterialDesignDataGrid}"
                          ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                          RowStyle="{StaticResource DataGridRowStyle}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          SelectionMode="Single"
                          SelectionChanged="InvoicesDataGrid_SelectionChanged"
                          MouseDoubleClick="InvoicesDataGrid_MouseDoubleClick"
                          Margin="16,0,16,16">

                    <DataGrid.Columns>
                        <!-- Invoice Number -->
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>

                        <!-- Supplier Name -->
                        <DataGridTextColumn Header="المورد" Binding="{Binding Supplier.Name}" Width="150"/>

                        <!-- Invoice Date -->
                        <DataGridTextColumn Header="تاريخ الفاتورة" Binding="{Binding InvoiceDate, StringFormat=dd/MM/yyyy}" Width="120"/>

                        <!-- Due Date -->
                        <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat=dd/MM/yyyy}" Width="120"/>

                        <!-- Amount -->
                        <DataGridTextColumn Header="المبلغ (د.ع)" Binding="{Binding Amount, Converter={StaticResource CurrencyConverter}}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Paid Amount -->
                        <DataGridTextColumn Header="المبلغ المدفوع (د.ع)" Binding="{Binding PaidAmount, Converter={StaticResource CurrencyConverter}}" Width="130">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Remaining Amount -->
                        <DataGridTextColumn Header="المبلغ المتبقي (د.ع)" Binding="{Binding RemainingAmount, Converter={StaticResource CurrencyConverter}}" Width="130">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="Foreground" Value="#FF6B6B"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Status -->
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                                            CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding Status, Converter={StaticResource StatusToTextConverter}}"
                                                   Foreground="White" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Actions -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                ToolTip="عرض التفاصيل" Click="ViewInvoiceButton_Click"
                                                CommandParameter="{Binding}" Margin="2">
                                            <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                        </Button>
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                ToolTip="إضافة دفعة" Click="AddPaymentButton_Click"
                                                CommandParameter="{Binding}" Margin="2"
                                                IsEnabled="{Binding CanAddPayment}">
                                            <materialDesign:PackIcon Kind="CashPlus" Width="16" Height="16"/>
                                        </Button>
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                ToolTip="طباعة" Click="PrintInvoiceButton_Click"
                                                CommandParameter="{Binding}" Margin="2">
                                            <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Status Bar -->
        <materialDesign:Card Grid.Row="3" Margin="16,8" materialDesign:ElevationAssist.Elevation="Dp1" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="StatusTextBlock" Text="جاهز" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock x:Name="RecordCountTextBlock" Text="عدد السجلات: 0" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock x:Name="FilterStatusTextBlock" Text="" VerticalAlignment="Center" FontStyle="Italic" Foreground="Gray"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="إجمالي المبلغ:" VerticalAlignment="Center" Margin="0,0,8,0" FontWeight="Bold"/>
                    <TextBlock x:Name="TotalAmountTextBlock" Text="0 د.ع" VerticalAlignment="Center" FontWeight="Bold" Foreground="#2196F3"/>
                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="16,0"/>
                    <TextBlock Text="المبلغ المتبقي:" VerticalAlignment="Center" Margin="0,0,8,0" FontWeight="Bold"/>
                    <TextBlock x:Name="RemainingAmountTextBlock" Text="0 د.ع" VerticalAlignment="Center" FontWeight="Bold" Foreground="#FF6B6B"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Invoice Form Overlay -->
        <controls:InvoiceFormOverlay x:Name="InvoiceFormOverlay"
                                   FormClosed="InvoiceFormOverlay_FormClosed"/>
    </Grid>
</Window>
