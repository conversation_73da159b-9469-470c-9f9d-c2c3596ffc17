using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Converters
{
    public class StatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is InvoiceStatus status)
            {
                return status switch
                {
                    InvoiceStatus.Unpaid => new SolidColorBrush(Color.FromRgb(244, 67, 54)), // Red
                    InvoiceStatus.PartiallyPaid => new SolidColorBrush(Color.FromRgb(255, 152, 0)), // Orange
                    InvoiceStatus.Paid => new SolidColorBrush(Color.FromRgb(76, 175, 80)), // Green
                    _ => new SolidColorBrush(Colors.Gray)
                };
            }
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
