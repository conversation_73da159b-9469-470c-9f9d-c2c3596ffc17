using System;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Controls
{
    public partial class OfferFormWindow : UserControl
    {
        public event EventHandler<OfferFormEventArgs>? FormClosed;
        
        private readonly OfferService _offerService;
        private string? _attachmentPath = null;
        private Offer? _currentOffer = null;
        private bool _isEditMode = false;

        public OfferFormWindow()
        {
            InitializeComponent();
            _offerService = App.ServiceProvider.GetRequiredService<OfferService>();
            InitializeForm();
        }

        public OfferFormWindow(Offer offer) : this()
        {
            _currentOffer = offer;
            _isEditMode = true;
            LoadOfferData(offer);
            HeaderTitle.Text = "تعديل العرض";
        }

        private void InitializeForm()
        {
            LoadScientificNames();
            ClearFields();
        }

        private void LoadScientificNames()
        {
            try
            {
                var names = _offerService.GetScientificNames().Select(s => s.Name).ToList();
                ScientificNameComboBox.ItemsSource = names;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأسماء العلمية: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void LoadOfferData(Offer offer)
        {
            ScientificOfficeTextBox.Text = offer.ScientificOffice;
            RepresentativeNameTextBox.Text = offer.RepresentativeName;
            RepresentativePhoneTextBox.Text = offer.RepresentativePhone;
            ScientificNameComboBox.Text = offer.ScientificName;
            TradeNameTextBox.Text = offer.TradeName;
            PriceTextBox.Text = offer.Price.ToString("F2");
            BonusOrDiscountTextBox.Text = offer.BonusOrDiscount;
            NotesTextBox.Text = offer.Notes;
            
            if (!string.IsNullOrEmpty(offer.AttachmentPath))
            {
                _attachmentPath = offer.AttachmentPath;
                AttachmentLabel.Text = Path.GetFileName(offer.AttachmentPath);
            }
        }

        private void AttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Filter = "جميع الملفات المدعومة|*.pdf;*.jpg;*.jpeg;*.png;*.xlsx;*.xls;*.doc;*.docx|" +
                        "ملفات PDF|*.pdf|" +
                        "ملفات الصور|*.jpg;*.jpeg;*.png|" +
                        "ملفات Excel|*.xlsx;*.xls|" +
                        "ملفات Word|*.doc;*.docx|" +
                        "جميع الملفات|*.*",
                Title = "اختر ملف مرفق"
            };

            if (dialog.ShowDialog() == true)
            {
                _attachmentPath = dialog.FileName;
                AttachmentLabel.Text = Path.GetFileName(dialog.FileName);
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                var offer = CreateOfferFromForm();
                
                if (_isEditMode && _currentOffer != null)
                {
                    offer.Id = _currentOffer.Id;
                    offer.CreatedAt = _currentOffer.CreatedAt;
                    _offerService.UpdateOffer(offer);
                    
                    MessageBox.Show("تم تحديث العرض بنجاح!", "نجح التحديث", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    offer.CreatedAt = DateTime.Now;
                    _offerService.AddOffer(offer);
                    
                    MessageBox.Show("تم إضافة العرض بنجاح!", "نجحت الإضافة", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }

                FormClosed?.Invoke(this, new OfferFormEventArgs(true, offer, _isEditMode ? "Edit" : "Add"));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العرض: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(ScientificOfficeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المكتب العلمي", "حقل مطلوب", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                ScientificOfficeTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(RepresentativeNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المندوب", "حقل مطلوب", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                RepresentativeNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ScientificNameComboBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المادة العلمية", "حقل مطلوب", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                ScientificNameComboBox.Focus();
                return false;
            }

            if (!decimal.TryParse(PriceTextBox.Text, out decimal price) || price <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر صحيح أكبر من صفر", "سعر غير صحيح", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                PriceTextBox.Focus();
                return false;
            }

            return true;
        }

        private Offer CreateOfferFromForm()
        {
            return new Offer
            {
                ScientificOffice = ScientificOfficeTextBox.Text.Trim(),
                RepresentativeName = RepresentativeNameTextBox.Text.Trim(),
                RepresentativePhone = RepresentativePhoneTextBox.Text.Trim(),
                ScientificName = ScientificNameComboBox.Text.Trim(),
                TradeName = TradeNameTextBox.Text.Trim(),
                Price = decimal.Parse(PriceTextBox.Text),
                BonusOrDiscount = BonusOrDiscountTextBox.Text.Trim(),
                Notes = NotesTextBox.Text.Trim(),
                AttachmentPath = _attachmentPath
            };
        }

        private void ClearFields()
        {
            ScientificOfficeTextBox.Clear();
            RepresentativeNameTextBox.Clear();
            RepresentativePhoneTextBox.Clear();
            ScientificNameComboBox.Text = "";
            TradeNameTextBox.Clear();
            PriceTextBox.Clear();
            BonusOrDiscountTextBox.Clear();
            NotesTextBox.Clear();
            _attachmentPath = null;
            AttachmentLabel.Text = "لم يتم اختيار ملف";
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من تفريغ جميع الحقول؟", "تأكيد التفريغ", 
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                ClearFields();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إلغاء العملية؟ سيتم فقدان البيانات المدخلة.", 
                                       "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                FormClosed?.Invoke(this, new OfferFormEventArgs(false, null, "Cancel"));
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            CancelButton_Click(sender, e);
        }
    }
}
