using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Data.Repositories
{
    public interface IPaymentRepository
    {
        Task<IEnumerable<Payment>> GetAllAsync();
        Task<Payment?> GetByIdAsync(int id);
        Task<Payment?> GetByReceiptNumberAsync(string receiptNumber);
        Task<IEnumerable<Payment>> GetByInvoiceIdAsync(int invoiceId);
        Task<IEnumerable<Payment>> GetBySupplierIdAsync(int supplierId);
        Task<Payment> AddAsync(Payment payment);
        Task<Payment> UpdateAsync(Payment payment);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> ExistsByReceiptNumberAsync(string receiptNumber, int? excludeId = null);
        Task<IEnumerable<Payment>> SearchAsync(string searchTerm);
        Task<IEnumerable<Payment>> GetFilteredAsync(PaymentFilter filter);
        Task<IEnumerable<Payment>> GetBySupplierAsync(int supplierId);
        Task<int> GetTotalCountAsync();
        Task<decimal> GetTotalAmountAsync();
        Task<IEnumerable<Payment>> GetRecentPaymentsAsync(int count = 10);
    }

    public class PaymentFilter
    {
        public int? InvoiceId { get; set; }
        public int? SupplierId { get; set; }
        public PaymentMethod? Method { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public string? SearchTerm { get; set; }
    }

    public class PaymentRepository : IPaymentRepository
    {
        private readonly DatabaseContext _context;

        public PaymentRepository(DatabaseContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Payment>> GetAllAsync()
        {
            // استخدام استعلام واحد مع Join بدلاً من Include متعدد
            return await _context.Payments
                .AsNoTracking()
                .Where(p => p.IsActive)
                .Select(p => new Payment
                {
                    Id = p.Id,
                    InvoiceId = p.InvoiceId,
                    Amount = p.Amount,
                    PaymentDate = p.PaymentDate,
                    Method = p.Method,
                    ReceiptNumber = p.ReceiptNumber,
                    Notes = p.Notes,
                    AttachmentPath = p.AttachmentPath,
                    CreatedDate = p.CreatedDate,
                    IsActive = p.IsActive,
                    Invoice = new Invoice
                    {
                        Id = p.Invoice.Id,
                        InvoiceNumber = p.Invoice.InvoiceNumber,
                        Amount = p.Invoice.Amount,
                        InvoiceDate = p.Invoice.InvoiceDate,
                        SupplierId = p.Invoice.SupplierId,
                        Supplier = new Supplier
                        {
                            Id = p.Invoice.Supplier.Id,
                            Name = p.Invoice.Supplier.Name,
                            Phone = p.Invoice.Supplier.Phone,
                            Email = p.Invoice.Supplier.Email
                        }
                    }
                })
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
        }

        public async Task<Payment?> GetByIdAsync(int id)
        {
            return await _context.Payments
                .AsNoTracking()
                .Where(p => p.Id == id && p.IsActive)
                .Select(p => new Payment
                {
                    Id = p.Id,
                    InvoiceId = p.InvoiceId,
                    Amount = p.Amount,
                    PaymentDate = p.PaymentDate,
                    Method = p.Method,
                    ReceiptNumber = p.ReceiptNumber,
                    Notes = p.Notes,
                    AttachmentPath = p.AttachmentPath,
                    CreatedDate = p.CreatedDate,
                    IsActive = p.IsActive,
                    Invoice = new Invoice
                    {
                        Id = p.Invoice.Id,
                        InvoiceNumber = p.Invoice.InvoiceNumber,
                        Amount = p.Invoice.Amount,
                        InvoiceDate = p.Invoice.InvoiceDate,
                        SupplierId = p.Invoice.SupplierId,
                        Supplier = new Supplier
                        {
                            Id = p.Invoice.Supplier.Id,
                            Name = p.Invoice.Supplier.Name,
                            Phone = p.Invoice.Supplier.Phone,
                            Email = p.Invoice.Supplier.Email
                        }
                    }
                })
                .FirstOrDefaultAsync();
        }

        public async Task<Payment?> GetByReceiptNumberAsync(string receiptNumber)
        {
            return await _context.Payments
                .Include(p => p.Invoice)
                .ThenInclude(i => i.Supplier)
                .FirstOrDefaultAsync(p => p.ReceiptNumber == receiptNumber && p.IsActive);
        }

        public async Task<IEnumerable<Payment>> GetByInvoiceIdAsync(int invoiceId)
        {
            return await _context.Payments
                .Where(p => p.InvoiceId == invoiceId && p.IsActive)
                .Include(p => p.Invoice)
                .ThenInclude(i => i.Supplier)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Payment>> GetBySupplierIdAsync(int supplierId)
        {
            return await _context.Payments
                .Where(p => p.Invoice.SupplierId == supplierId && p.IsActive)
                .Include(p => p.Invoice)
                .ThenInclude(i => i.Supplier)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
        }

        public async Task<Payment> AddAsync(Payment payment)
        {
            payment.CreatedDate = DateTime.Now;
            payment.IsActive = true;

            _context.Payments.Add(payment);

            // Update invoice paid amount and status
            var invoice = await _context.Invoices.FindAsync(payment.InvoiceId);
            if (invoice != null)
            {
                var oldStatus = invoice.Status;
                invoice.PaidAmount += payment.Amount;
                invoice.UpdateStatus();
                invoice.UpdatedDate = DateTime.Now;

                // Log status change for debugging with payment details
                if (oldStatus != invoice.Status)
                {
                    string paymentInfo = payment.DiscountAmount > 0
                        ? $"Payment: {payment.Amount:F0} IQD, Discount: {payment.DiscountAmount:F0} IQD, Status: {payment.Status}"
                        : $"Payment: {payment.Amount:F0} IQD, Status: {payment.Status}";

                    System.Diagnostics.Debug.WriteLine($"Invoice {invoice.InvoiceNumber} status changed from {oldStatus} to {invoice.Status} - {paymentInfo}");
                }
            }

            await _context.SaveChangesAsync();
            return payment;
        }

        public async Task<Payment> UpdateAsync(Payment payment)
        {
            var existingPayment = await _context.Payments.AsNoTracking().FirstOrDefaultAsync(p => p.Id == payment.Id);
            if (existingPayment == null)
                throw new InvalidOperationException("Payment not found");

            payment.UpdatedDate = DateTime.Now;
            
            // Update invoice paid amount
            var invoice = await _context.Invoices.FindAsync(payment.InvoiceId);
            if (invoice != null)
            {
                // Remove old amount and add new amount
                invoice.PaidAmount = invoice.PaidAmount - existingPayment.Amount + payment.Amount;
                invoice.UpdateStatus();
                invoice.UpdatedDate = DateTime.Now;
            }
            
            _context.Entry(payment).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return payment;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var payment = await _context.Payments.FindAsync(id);
            if (payment == null) return false;

            // Update invoice paid amount
            var invoice = await _context.Invoices.FindAsync(payment.InvoiceId);
            if (invoice != null)
            {
                invoice.PaidAmount -= payment.Amount;
                invoice.UpdateStatus();
                invoice.UpdatedDate = DateTime.Now;
            }

            // Soft delete
            payment.IsActive = false;
            payment.UpdatedDate = DateTime.Now;
            
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Payments.AnyAsync(p => p.Id == id && p.IsActive);
        }

        public async Task<bool> ExistsByReceiptNumberAsync(string receiptNumber, int? excludeId = null)
        {
            var query = _context.Payments.Where(p => p.ReceiptNumber == receiptNumber && p.IsActive);
            
            if (excludeId.HasValue)
                query = query.Where(p => p.Id != excludeId.Value);
            
            return await query.AnyAsync();
        }

        public async Task<IEnumerable<Payment>> SearchAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllAsync();

            searchTerm = searchTerm.Trim().ToLower();

            return await _context.Payments
                .Where(p => p.IsActive &&
                           (p.ReceiptNumber.ToLower().Contains(searchTerm) ||
                            p.Invoice.InvoiceNumber.ToLower().Contains(searchTerm) ||
                            p.Invoice.Supplier.Name.ToLower().Contains(searchTerm) ||
                            (p.Notes != null && p.Notes.ToLower().Contains(searchTerm))))
                .Include(p => p.Invoice)
                .ThenInclude(i => i.Supplier)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Payment>> GetFilteredAsync(PaymentFilter filter)
        {
            var query = _context.Payments.Where(p => p.IsActive);

            if (filter.InvoiceId.HasValue)
                query = query.Where(p => p.InvoiceId == filter.InvoiceId.Value);

            if (filter.SupplierId.HasValue)
                query = query.Where(p => p.Invoice.SupplierId == filter.SupplierId.Value);

            if (filter.Method.HasValue)
                query = query.Where(p => p.Method == filter.Method.Value);

            if (filter.FromDate.HasValue)
                query = query.Where(p => p.PaymentDate >= filter.FromDate.Value);

            if (filter.ToDate.HasValue)
                query = query.Where(p => p.PaymentDate <= filter.ToDate.Value);

            if (filter.MinAmount.HasValue)
                query = query.Where(p => p.Amount >= filter.MinAmount.Value);

            if (filter.MaxAmount.HasValue)
                query = query.Where(p => p.Amount <= filter.MaxAmount.Value);

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                var searchTerm = filter.SearchTerm.Trim().ToLower();
                query = query.Where(p => p.ReceiptNumber.ToLower().Contains(searchTerm) ||
                                        p.Invoice.InvoiceNumber.ToLower().Contains(searchTerm) ||
                                        p.Invoice.Supplier.Name.ToLower().Contains(searchTerm) ||
                                        (p.Notes != null && p.Notes.ToLower().Contains(searchTerm)));
            }

            return await query
                .Include(p => p.Invoice)
                .ThenInclude(i => i.Supplier)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Payment>> GetBySupplierAsync(int supplierId)
        {
            return await _context.Payments
                .Where(p => p.IsActive && p.Invoice.SupplierId == supplierId)
                .Include(p => p.Invoice)
                    .ThenInclude(i => i.Supplier)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
        }

        public async Task<int> GetTotalCountAsync()
        {
            return await _context.Payments.CountAsync(p => p.IsActive);
        }

        public async Task<decimal> GetTotalAmountAsync()
        {
            return await _context.Payments
                .Where(p => p.IsActive)
                .SumAsync(p => p.Amount);
        }

        public async Task<IEnumerable<Payment>> GetRecentPaymentsAsync(int count = 10)
        {
            return await _context.Payments
                .Where(p => p.IsActive)
                .Include(p => p.Invoice)
                .ThenInclude(i => i.Supplier)
                .OrderByDescending(p => p.PaymentDate)
                .Take(count)
                .ToListAsync();
        }
    }
}
