<UserControl x:Class="HR_InvoiceArchiver.Controls.Dashboard.RecentActivitiesControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Styles/MaterialDesignStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Recent Invoices -->
        <materialDesign:Card Grid.Column="0" Style="{StaticResource ModernCardStyle}" Margin="4,8,8,8">
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Grid Grid.Row="0" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الفواتير الحديثة"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="#1976D2" FontWeight="Bold"/>
                        <TextBlock Text="آخر الفواتير المضافة"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Opacity="0.7" Margin="0,2,0,0"/>
                    </StackPanel>

                    <Button Grid.Column="1" 
                          Style="{StaticResource MaterialDesignFlatButton}"
                          Content="عرض الكل" 
                          Click="ViewAllInvoicesButton_Click"/>
                </Grid>

                <!-- Recent Invoices List -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Height="250">
                    <ItemsControl x:Name="RecentInvoicesItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F8F9FA" CornerRadius="8" 
                                      Margin="0,0,0,8" Padding="12">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Column="0" 
                                                               Kind="FileDocument" 
                                                               Width="20" Height="20"
                                                               Foreground="#1976D2" 
                                                               VerticalAlignment="Center"/>

                                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                            <TextBlock Text="{Binding InvoiceNumber}" 
                                                     FontWeight="Medium" FontSize="14"/>
                                            <TextBlock Text="{Binding SupplierName}" 
                                                     FontSize="12" Foreground="#666"/>
                                            <TextBlock Text="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}" 
                                                     FontSize="11" Foreground="#999"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                                            <TextBlock Text="{Binding Amount, StringFormat='{}{0:N0} د.ع'}" 
                                                     FontWeight="Bold" FontSize="14"
                                                     HorizontalAlignment="Right"/>
                                            <Border Background="{Binding StatusColor}" 
                                                  CornerRadius="10" Padding="6,2"
                                                  HorizontalAlignment="Right" Margin="0,2,0,0">
                                                <TextBlock Text="{Binding StatusText}" 
                                                         FontSize="10" Foreground="White"/>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </materialDesign:Card>

        <!-- Alerts Panel -->
        <materialDesign:Card Grid.Column="1" Style="{StaticResource ModernCardStyle}" Margin="8,8,4,8">
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Grid Grid.Row="0" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="التنبيهات"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="#F44336" FontWeight="Bold"/>
                        <TextBlock Text="تنبيهات مهمة تحتاج انتباه"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Opacity="0.7" Margin="0,2,0,0"/>
                    </StackPanel>

                    <Button Grid.Column="1" 
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="مسح جميع التنبيهات"
                          Click="ClearAlertsButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="18" Height="18"/>
                    </Button>
                </Grid>

                <!-- Alerts List -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Height="250">
                    <ItemsControl x:Name="AlertsItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#FFF3E0" CornerRadius="8" 
                                      Margin="0,0,0,8" Padding="12">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Column="0" 
                                                               Kind="{Binding IconKind}" 
                                                               Width="20" Height="20"
                                                               Foreground="{Binding IconColor}" 
                                                               VerticalAlignment="Top"
                                                               Margin="0,2,0,0"/>

                                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                            <TextBlock Text="{Binding Title}" 
                                                     FontWeight="Medium" FontSize="14"/>
                                            <TextBlock Text="{Binding Message}" 
                                                     FontSize="12" Foreground="#666"
                                                     TextWrapping="Wrap"/>
                                            <TextBlock Text="{Binding Timestamp, StringFormat='{}{0:HH:mm}'}" 
                                                     FontSize="10" Foreground="#999"
                                                     Margin="0,4,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- No Alerts Message -->
                <StackPanel x:Name="NoAlertsPanel" 
                          Grid.Row="1"
                          HorizontalAlignment="Center" 
                          VerticalAlignment="Center"
                          Visibility="Collapsed">
                    <materialDesign:PackIcon Kind="CheckCircle" 
                                           Width="48" Height="48" 
                                           Foreground="#4CAF50" Margin="0,0,0,8"/>
                    <TextBlock Text="لا توجد تنبيهات" 
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Foreground="#4CAF50" HorizontalAlignment="Center"/>
                    <TextBlock Text="جميع الأمور تسير بشكل طبيعي" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             Foreground="#666" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
