using System.Text.RegularExpressions;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Utils
{
    public static class Validator
    {
        // Email validation
        private static readonly Regex EmailRegex = new Regex(
            @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
            RegexOptions.Compiled | RegexOptions.IgnoreCase);

        // Phone validation (Egyptian format)
        private static readonly Regex PhoneRegex = new Regex(
            @"^(\+20|0)?1[0-9]{9}$|^(\+20|0)?[2-9][0-9]{7,8}$",
            RegexOptions.Compiled);

        // Invoice number validation
        private static readonly Regex InvoiceNumberRegex = new Regex(
            @"^[A-Za-z0-9\-_/]+$",
            RegexOptions.Compiled);

        // Receipt number validation
        private static readonly Regex ReceiptNumberRegex = new Regex(
            @"^[A-Za-z0-9\-_/]+$",
            RegexOptions.Compiled);

        public static class SupplierValidator
        {
            public static ValidationResult ValidateSupplier(Supplier supplier)
            {
                var result = new ValidationResult();

                // Name validation
                if (string.IsNullOrWhiteSpace(supplier.Name))
                {
                    result.AddError("اسم المورد مطلوب");
                }
                else if (supplier.Name.Length > 200)
                {
                    result.AddError("اسم المورد لا يجب أن يتجاوز 200 حرف");
                }
                else if (supplier.Name.Length < 2)
                {
                    result.AddError("اسم المورد يجب أن يكون على الأقل حرفين");
                }

                // Contact person validation
                if (!string.IsNullOrEmpty(supplier.ContactPerson) && supplier.ContactPerson.Length > 100)
                {
                    result.AddError("اسم الشخص المسؤول لا يجب أن يتجاوز 100 حرف");
                }

                // Phone validation
                if (!string.IsNullOrEmpty(supplier.Phone))
                {
                    if (supplier.Phone.Length > 20)
                    {
                        result.AddError("رقم الهاتف لا يجب أن يتجاوز 20 رقم");
                    }
                    else if (!IsValidPhone(supplier.Phone))
                    {
                        result.AddError("رقم الهاتف غير صحيح");
                    }
                }

                // Email validation
                if (!string.IsNullOrEmpty(supplier.Email))
                {
                    if (supplier.Email.Length > 100)
                    {
                        result.AddError("البريد الإلكتروني لا يجب أن يتجاوز 100 حرف");
                    }
                    else if (!IsValidEmail(supplier.Email))
                    {
                        result.AddError("البريد الإلكتروني غير صحيح");
                    }
                }

                // Address validation
                if (!string.IsNullOrEmpty(supplier.Address) && supplier.Address.Length > 500)
                {
                    result.AddError("العنوان لا يجب أن يتجاوز 500 حرف");
                }

                // Notes validation
                if (!string.IsNullOrEmpty(supplier.Notes) && supplier.Notes.Length > 1000)
                {
                    result.AddError("الملاحظات لا يجب أن تتجاوز 1000 حرف");
                }

                return result;
            }
        }

        public static class InvoiceValidator
        {
            public static ValidationResult ValidateInvoice(Invoice invoice)
            {
                var result = new ValidationResult();

                // Invoice number validation
                if (string.IsNullOrWhiteSpace(invoice.InvoiceNumber))
                {
                    result.AddError("رقم الفاتورة مطلوب");
                }
                else if (invoice.InvoiceNumber.Length > 50)
                {
                    result.AddError("رقم الفاتورة لا يجب أن يتجاوز 50 حرف");
                }
                else if (!IsValidInvoiceNumber(invoice.InvoiceNumber))
                {
                    result.AddError("رقم الفاتورة يحتوي على أحرف غير مسموحة");
                }

                // Supplier validation
                if (invoice.SupplierId <= 0)
                {
                    result.AddError("يجب اختيار المورد");
                }

                // Amount validation
                if (invoice.Amount <= 0)
                {
                    result.AddError("مبلغ الفاتورة يجب أن يكون أكبر من صفر");
                }
                else if (invoice.Amount > 999999999.99m)
                {
                    result.AddError("مبلغ الفاتورة كبير جداً");
                }

                // Paid amount validation
                if (invoice.PaidAmount < 0)
                {
                    result.AddError("المبلغ المدفوع لا يمكن أن يكون سالب");
                }
                else if (invoice.PaidAmount > invoice.Amount)
                {
                    result.AddError("المبلغ المدفوع لا يمكن أن يكون أكبر من مبلغ الفاتورة");
                }

                // Date validation
                if (invoice.InvoiceDate > DateTime.Now.AddDays(1))
                {
                    result.AddError("تاريخ الفاتورة لا يمكن أن يكون في المستقبل");
                }
                else if (invoice.InvoiceDate < new DateTime(2000, 1, 1))
                {
                    result.AddError("تاريخ الفاتورة غير صحيح");
                }

                // Due date validation
                if (invoice.DueDate.HasValue && invoice.DueDate.Value < invoice.InvoiceDate)
                {
                    result.AddError("تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الفاتورة");
                }

                // Notes validation
                if (!string.IsNullOrEmpty(invoice.Notes) && invoice.Notes.Length > 1000)
                {
                    result.AddError("الملاحظات لا يجب أن تتجاوز 1000 حرف");
                }

                return result;
            }
        }

        public static class PaymentValidator
        {
            public static ValidationResult ValidatePayment(Payment payment)
            {
                var result = new ValidationResult();

                // Receipt number validation
                if (string.IsNullOrWhiteSpace(payment.ReceiptNumber))
                {
                    result.AddError("رقم الوصل مطلوب");
                }
                else if (payment.ReceiptNumber.Length > 50)
                {
                    result.AddError("رقم الوصل لا يجب أن يتجاوز 50 حرف");
                }
                else if (!IsValidReceiptNumber(payment.ReceiptNumber))
                {
                    result.AddError("رقم الوصل يحتوي على أحرف غير مسموحة");
                }

                // Invoice validation
                if (payment.InvoiceId <= 0)
                {
                    result.AddError("يجب اختيار الفاتورة");
                }

                // Amount validation
                if (payment.Amount <= 0)
                {
                    result.AddError("مبلغ الوصل يجب أن يكون أكبر من صفر");
                }
                else if (payment.Amount > 999999999.99m)
                {
                    result.AddError("مبلغ الوصل كبير جداً");
                }

                // Date validation
                if (payment.PaymentDate > DateTime.Now.AddDays(1))
                {
                    result.AddError("تاريخ الدفع لا يمكن أن يكون في المستقبل");
                }
                else if (payment.PaymentDate < new DateTime(2000, 1, 1))
                {
                    result.AddError("تاريخ الدفع غير صحيح");
                }



                // Notes validation
                if (!string.IsNullOrEmpty(payment.Notes) && payment.Notes.Length > 1000)
                {
                    result.AddError("الملاحظات لا يجب أن تتجاوز 1000 حرف");
                }

                return result;
            }

            public static ValidationResult ValidatePaymentAmount(Payment payment, Invoice invoice)
            {
                var result = new ValidationResult();

                if (invoice == null)
                {
                    result.AddError("الفاتورة غير موجودة");
                    return result;
                }

                if (payment.Amount > invoice.RemainingAmount)
                {
                    result.AddError($"مبلغ الوصل ({payment.Amount:C}) أكبر من المبلغ المتبقي ({invoice.RemainingAmount:C})");
                }

                if (invoice.Status == InvoiceStatus.Paid)
                {
                    result.AddError("الفاتورة مسددة بالكامل ولا تحتاج لمدفوعات إضافية");
                }

                return result;
            }
        }

        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            return EmailRegex.IsMatch(email);
        }

        public static bool IsValidPhone(string phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
                return false;

            // Remove spaces and dashes
            phone = phone.Replace(" ", "").Replace("-", "");

            return PhoneRegex.IsMatch(phone);
        }

        public static bool IsValidInvoiceNumber(string invoiceNumber)
        {
            if (string.IsNullOrWhiteSpace(invoiceNumber))
                return false;

            return InvoiceNumberRegex.IsMatch(invoiceNumber);
        }

        public static bool IsValidReceiptNumber(string receiptNumber)
        {
            if (string.IsNullOrWhiteSpace(receiptNumber))
                return false;

            return ReceiptNumberRegex.IsMatch(receiptNumber);
        }

        public static bool IsValidAmount(decimal amount)
        {
            return amount > 0 && amount <= 999999999.99m;
        }

        public static bool IsValidDate(DateTime date)
        {
            return date >= new DateTime(2000, 1, 1) && date <= DateTime.Now.AddDays(1);
        }

        public static string FormatPhoneNumber(string phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
                return string.Empty;

            // Remove spaces and dashes
            phone = phone.Replace(" ", "").Replace("-", "");

            // Format Egyptian mobile numbers
            if (phone.StartsWith("01") && phone.Length == 11)
            {
                return $"{phone.Substring(0, 4)}-{phone.Substring(4, 3)}-{phone.Substring(7, 4)}";
            }

            // Format landline numbers
            if (phone.StartsWith("0") && phone.Length >= 8 && phone.Length <= 10)
            {
                return phone.Length == 8 
                    ? $"{phone.Substring(0, 2)}-{phone.Substring(2, 3)}-{phone.Substring(5, 3)}"
                    : phone;
            }

            return phone;
        }
    }

    public class ValidationResult
    {
        public bool IsValid => !Errors.Any();
        public List<string> Errors { get; } = new List<string>();

        public void AddError(string error)
        {
            if (!string.IsNullOrWhiteSpace(error))
                Errors.Add(error);
        }

        public void AddErrors(IEnumerable<string> errors)
        {
            foreach (var error in errors)
                AddError(error);
        }

        public string GetErrorsAsString(string separator = "\n")
        {
            return string.Join(separator, Errors);
        }
    }
}
