using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace HR_InvoiceArchiver.Services
{
    public interface IRetryService
    {
        Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            int maxRetries = 3,
            TimeSpan? delay = null,
            Func<Exception, bool>? shouldRetry = null,
            CancellationToken cancellationToken = default);

        Task ExecuteWithRetryAsync(
            Func<Task> operation,
            int maxRetries = 3,
            TimeSpan? delay = null,
            Func<Exception, bool>? shouldRetry = null,
            CancellationToken cancellationToken = default);

        T ExecuteWithRetry<T>(
            Func<T> operation,
            int maxRetries = 3,
            TimeSpan? delay = null,
            Func<Exception, bool>? shouldRetry = null);

        void ExecuteWithRetry(
            Action operation,
            int maxRetries = 3,
            TimeSpan? delay = null,
            Func<Exception, bool>? shouldRetry = null);
    }

    public class RetryService : IRetryService
    {
        private readonly ILogger<RetryService> _logger;
        private readonly IGlobalExceptionHandler _exceptionHandler;

        public RetryService(ILogger<RetryService> logger, IGlobalExceptionHandler exceptionHandler)
        {
            _logger = logger;
            _exceptionHandler = exceptionHandler;
        }

        public async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            int maxRetries = 3,
            TimeSpan? delay = null,
            Func<Exception, bool>? shouldRetry = null,
            CancellationToken cancellationToken = default)
        {
            var actualDelay = delay ?? TimeSpan.FromSeconds(1);
            var defaultShouldRetry = shouldRetry ?? DefaultShouldRetry;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var result = await operation();
                    if (attempt > 1)
                    {
                        _logger.LogInformation("Operation succeeded on attempt {Attempt}", attempt);
                    }
                    return result;
                }
                catch (Exception ex) when (attempt < maxRetries && defaultShouldRetry(ex))
                {
                    _logger.LogWarning(ex, "Operation failed on attempt {Attempt}/{MaxRetries}. Retrying in {Delay}ms", 
                        attempt, maxRetries, actualDelay.TotalMilliseconds);

                    await Task.Delay(actualDelay, cancellationToken);
                    
                    // Exponential backoff
                    actualDelay = TimeSpan.FromMilliseconds(actualDelay.TotalMilliseconds * 1.5);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Operation failed permanently after {Attempt} attempts", attempt);
                    _exceptionHandler.LogError($"Retry operation failed after {attempt} attempts", ex);
                    throw;
                }
            }

            throw new InvalidOperationException("This should never be reached");
        }

        public async Task ExecuteWithRetryAsync(
            Func<Task> operation,
            int maxRetries = 3,
            TimeSpan? delay = null,
            Func<Exception, bool>? shouldRetry = null,
            CancellationToken cancellationToken = default)
        {
            await ExecuteWithRetryAsync(async () =>
            {
                await operation();
                return true; // Dummy return value
            }, maxRetries, delay, shouldRetry, cancellationToken);
        }

        public T ExecuteWithRetry<T>(
            Func<T> operation,
            int maxRetries = 3,
            TimeSpan? delay = null,
            Func<Exception, bool>? shouldRetry = null)
        {
            var actualDelay = delay ?? TimeSpan.FromSeconds(1);
            var defaultShouldRetry = shouldRetry ?? DefaultShouldRetry;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var result = operation();
                    if (attempt > 1)
                    {
                        _logger.LogInformation("Operation succeeded on attempt {Attempt}", attempt);
                    }
                    return result;
                }
                catch (Exception ex) when (attempt < maxRetries && defaultShouldRetry(ex))
                {
                    _logger.LogWarning(ex, "Operation failed on attempt {Attempt}/{MaxRetries}. Retrying in {Delay}ms", 
                        attempt, maxRetries, actualDelay.TotalMilliseconds);

                    Thread.Sleep(actualDelay);
                    
                    // Exponential backoff
                    actualDelay = TimeSpan.FromMilliseconds(actualDelay.TotalMilliseconds * 1.5);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Operation failed permanently after {Attempt} attempts", attempt);
                    _exceptionHandler.LogError($"Retry operation failed after {attempt} attempts", ex);
                    throw;
                }
            }

            throw new InvalidOperationException("This should never be reached");
        }

        public void ExecuteWithRetry(
            Action operation,
            int maxRetries = 3,
            TimeSpan? delay = null,
            Func<Exception, bool>? shouldRetry = null)
        {
            ExecuteWithRetry(() =>
            {
                operation();
                return true; // Dummy return value
            }, maxRetries, delay, shouldRetry);
        }

        private static bool DefaultShouldRetry(Exception exception)
        {
            return exception switch
            {
                // Network-related exceptions
                System.Net.Http.HttpRequestException => true,
                System.Net.WebException => true,
                TaskCanceledException => true,
                TimeoutException => true,
                
                // IO-related exceptions - specific ones first
                System.IO.DirectoryNotFoundException => false, // Don't retry for missing directories
                System.IO.FileNotFoundException => false, // Don't retry for missing files
                System.IO.IOException => true, // General IO issues

                // Database-related exceptions
                Microsoft.Data.Sqlite.SqliteException => true,
                System.Data.Common.DbException => true,
                
                // Memory issues - don't retry
                OutOfMemoryException => false,
                
                // Security issues - don't retry
                UnauthorizedAccessException => false,
                System.Security.SecurityException => false,
                
                // Argument issues - don't retry (specific first)
                ArgumentNullException => false,
                ArgumentException => false,
                
                // Default: retry for unknown exceptions
                _ => true
            };
        }
    }

    /// <summary>
    /// Extension methods for common retry scenarios
    /// </summary>
    public static class RetryExtensions
    {
        public static async Task<T> WithDatabaseRetryAsync<T>(this IRetryService retryService, Func<Task<T>> operation)
        {
            return await retryService.ExecuteWithRetryAsync(
                operation,
                maxRetries: 3,
                delay: TimeSpan.FromMilliseconds(500),
                shouldRetry: ex => ex is System.Data.Common.DbException or Microsoft.Data.Sqlite.SqliteException
            );
        }

        public static async Task WithDatabaseRetryAsync(this IRetryService retryService, Func<Task> operation)
        {
            await retryService.ExecuteWithRetryAsync(
                operation,
                maxRetries: 3,
                delay: TimeSpan.FromMilliseconds(500),
                shouldRetry: ex => ex is System.Data.Common.DbException or Microsoft.Data.Sqlite.SqliteException
            );
        }

        public static async Task<T> WithNetworkRetryAsync<T>(this IRetryService retryService, Func<Task<T>> operation)
        {
            return await retryService.ExecuteWithRetryAsync(
                operation,
                maxRetries: 5,
                delay: TimeSpan.FromSeconds(2),
                shouldRetry: ex => ex is System.Net.Http.HttpRequestException or 
                                   System.Net.WebException or 
                                   TaskCanceledException or 
                                   TimeoutException
            );
        }

        public static async Task WithNetworkRetryAsync(this IRetryService retryService, Func<Task> operation)
        {
            await retryService.ExecuteWithRetryAsync(
                operation,
                maxRetries: 5,
                delay: TimeSpan.FromSeconds(2),
                shouldRetry: ex => ex is System.Net.Http.HttpRequestException or 
                                   System.Net.WebException or 
                                   TaskCanceledException or 
                                   TimeoutException
            );
        }

        public static async Task<T> WithFileRetryAsync<T>(this IRetryService retryService, Func<Task<T>> operation)
        {
            return await retryService.ExecuteWithRetryAsync(
                operation,
                maxRetries: 3,
                delay: TimeSpan.FromMilliseconds(200),
                shouldRetry: ex => ex is System.IO.IOException
            );
        }

        public static async Task WithFileRetryAsync(this IRetryService retryService, Func<Task> operation)
        {
            await retryService.ExecuteWithRetryAsync(
                operation,
                maxRetries: 3,
                delay: TimeSpan.FromMilliseconds(200),
                shouldRetry: ex => ex is System.IO.IOException
            );
        }
    }
}
