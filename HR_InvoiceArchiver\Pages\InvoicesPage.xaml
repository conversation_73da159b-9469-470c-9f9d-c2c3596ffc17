<UserControl x:Class="HR_InvoiceArchiver.Pages.InvoicesPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:HR_InvoiceArchiver.Controls"
             xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
             FlowDirection="RightToLeft"
             Background="#F8F9FA">

    <UserControl.Resources>
        <!-- Converters -->
        <converters:FirstLetterConverter x:Key="FirstLetterConverter"/>
        <converters:StringNullOrEmptyConverter x:Key="StringNullOrEmptyConverter"/>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Opacity="0.3" BlurRadius="10" ShadowDepth="2"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Search Box Style -->
        <Style x:Key="SearchBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="10">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                                <TextBlock x:Name="PlaceholderText"
                                         Text="🔍 البحث في الفواتير..."
                                         Foreground="#9E9E9E"
                                         VerticalAlignment="Center"
                                         Margin="{TemplateBinding Padding}"
                                         IsHitTestVisible="False"
                                         Visibility="Collapsed"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="Text" Value="">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="#007BFF"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#007BFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Primary Button Style -->
        <Style x:Key="ModernPrimaryButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="48"/>
            <Setter Property="Padding" Value="24,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder" CornerRadius="12">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#007BFF" Offset="0"/>
                                    <GradientStop Color="#0056B3" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <Border.Effect>
                                <DropShadowEffect Color="#007BFF" Opacity="0.4" BlurRadius="12" ShadowDepth="4"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                            TextBlock.Foreground="White"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#28A745" Offset="0"/>
                                            <GradientStop Color="#1E7E34" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="ButtonBorder" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        </Style>

        <!-- Modern Secondary Button Style -->
        <Style x:Key="ModernSecondaryButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="44"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder" CornerRadius="10" BorderThickness="1.5">
                            <Border.Background>
                                <SolidColorBrush Color="White"/>
                            </Border.Background>
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#E6F3FF" Offset="0"/>
                                    <GradientStop Color="#CCE5FF" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.BorderBrush>
                            <Border.Effect>
                                <DropShadowEffect Color="#90A4AE" Opacity="0.2" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                            TextBlock.Foreground="#2C3E50"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#E3F2FD" Offset="0"/>
                                            <GradientStop Color="#BBDEFB" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="ButtonBorder" Property="BorderBrush">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#007BFF" Offset="0"/>
                                            <GradientStop Color="#0056B3" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="TextBlock.Foreground" Value="#007BFF"/>
                                <Setter TargetName="ButtonBorder" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#E6F3FF"/>
                                <Setter TargetName="ButtonBorder" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        </Style>

        <!-- Modern Icon Button Style -->
        <Style x:Key="ModernIconButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="44"/>
            <Setter Property="Height" Value="44"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder" CornerRadius="12" Background="White" BorderThickness="1">
                            <Border.BorderBrush>
                                <SolidColorBrush Color="#CCE5FF"/>
                            </Border.BorderBrush>
                            <Border.Effect>
                                <DropShadowEffect Color="#90A4AE" Opacity="0.15" BlurRadius="6" ShadowDepth="1"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                            TextBlock.Foreground="#495057"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#E3F2FD" Offset="0"/>
                                            <GradientStop Color="#BBDEFB" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="#007BFF"/>
                                <Setter Property="TextBlock.Foreground" Value="#007BFF"/>
                                <Setter TargetName="ButtonBorder" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <!-- Main Content -->
        <Grid x:Name="MainContentGrid" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Enhanced Header Section -->
            <Border Grid.Row="0" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FFFFFF" Offset="0"/>
                        <GradientStop Color="#F8F9FA" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Enhanced Title and Icon -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Border CornerRadius="16" Padding="16" Margin="0,0,20,0">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#E3F2FD" Offset="0"/>
                                    <GradientStop Color="#BBDEFB" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <Border.Effect>
                                <DropShadowEffect Color="#2196F3" Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <TextBlock Text="📋" FontSize="28"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إدارة الفواتير المتقدمة"
                                     FontSize="26" FontWeight="Bold"
                                     Foreground="#1565C0"/>
                            <TextBlock Text="نظام شامل لإدارة وتتبع جميع الفواتير والمدفوعات"
                                     FontSize="15"
                                     Foreground="#546E7A"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Quick Actions -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0,0,15,0">
                        <!-- Export Button -->
                        <Button x:Name="ExportButton"
                                Style="{StaticResource ModernSecondaryButtonStyle}"
                                Click="ExportButton_Click"
                                Margin="0,0,10,0"
                                ToolTip="تصدير البيانات">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📤" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تصدير" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Button>

                        <!-- Print Button -->
                        <Button x:Name="PrintButton"
                                Style="{StaticResource ModernSecondaryButtonStyle}"
                                Click="PrintButton_Click"
                                ToolTip="طباعة التقرير">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🖨️" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="طباعة" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- Enhanced Add Invoice Button -->
                    <Button Grid.Column="3"
                            x:Name="AddInvoiceButton"
                            Style="{StaticResource ModernPrimaryButtonStyle}"
                            Click="AddInvoiceButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <Border Background="White" CornerRadius="8" Padding="6" Margin="0,0,12,0">
                                <TextBlock Text="✨" FontSize="16"/>
                            </Border>
                            <TextBlock Text="إضافة فاتورة جديدة" FontWeight="Bold" FontSize="15"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </Border>

            <!-- Enhanced Statistics Cards -->
            <Border Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Total Invoices Card -->
                    <Border Grid.Column="0" CornerRadius="16" Padding="24" Margin="0,0,12,0" Cursor="Hand">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#E3F2FD" Offset="0"/>
                                <GradientStop Color="#BBDEFB" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#2196F3" Opacity="0.25" BlurRadius="12" ShadowDepth="4"/>
                        </Border.Effect>
                        <StackPanel>
                            <Border Background="White" CornerRadius="12" Padding="8" HorizontalAlignment="Left" Margin="0,0,0,12">
                                <TextBlock Text="📊" FontSize="24"/>
                            </Border>
                            <TextBlock Text="إجمالي الفواتير" FontWeight="Bold" FontSize="14" Foreground="#1565C0" Margin="0,0,0,8"/>
                            <TextBlock x:Name="TotalInvoicesText" Text="0" FontSize="28" FontWeight="Bold" Foreground="#0D47A1"/>
                            <TextBlock Text="فاتورة" FontSize="13" Foreground="#546E7A"/>
                        </StackPanel>
                    </Border>

                    <!-- Unpaid Invoices Card -->
                    <Border Grid.Column="1" CornerRadius="16" Padding="24" Margin="0,0,12,0" Cursor="Hand">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FFEBEE" Offset="0"/>
                                <GradientStop Color="#FFCDD2" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#F44336" Opacity="0.25" BlurRadius="12" ShadowDepth="4"/>
                        </Border.Effect>
                        <StackPanel>
                            <Border Background="White" CornerRadius="12" Padding="8" HorizontalAlignment="Left" Margin="0,0,0,12">
                                <TextBlock Text="⏳" FontSize="24"/>
                            </Border>
                            <TextBlock Text="غير مسددة" FontWeight="Bold" FontSize="14" Foreground="#C62828" Margin="0,0,0,8"/>
                            <TextBlock x:Name="UnpaidInvoicesText" Text="0" FontSize="28" FontWeight="Bold" Foreground="#B71C1C"/>
                            <TextBlock Text="فاتورة" FontSize="13" Foreground="#546E7A"/>
                        </StackPanel>
                    </Border>

                    <!-- Partially Paid Card -->
                    <Border Grid.Column="2" CornerRadius="16" Padding="24" Margin="0,0,12,0" Cursor="Hand">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FFF3E0" Offset="0"/>
                                <GradientStop Color="#FFE0B2" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#FF9800" Opacity="0.25" BlurRadius="12" ShadowDepth="4"/>
                        </Border.Effect>
                        <StackPanel>
                            <Border Background="White" CornerRadius="12" Padding="8" HorizontalAlignment="Left" Margin="0,0,0,12">
                                <TextBlock Text="⚡" FontSize="24"/>
                            </Border>
                            <TextBlock Text="مسددة جزئياً" FontWeight="Bold" FontSize="14" Foreground="#EF6C00" Margin="0,0,0,8"/>
                            <TextBlock x:Name="PartiallyPaidInvoicesText" Text="0" FontSize="28" FontWeight="Bold" Foreground="#E65100"/>
                            <TextBlock Text="فاتورة" FontSize="13" Foreground="#546E7A"/>
                        </StackPanel>
                    </Border>

                    <!-- Paid Invoices Card -->
                    <Border Grid.Column="3" CornerRadius="16" Padding="24" Margin="0,0,12,0" Cursor="Hand">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#E8F5E8" Offset="0"/>
                                <GradientStop Color="#C8E6C9" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#4CAF50" Opacity="0.25" BlurRadius="12" ShadowDepth="4"/>
                        </Border.Effect>
                        <StackPanel>
                            <Border Background="White" CornerRadius="12" Padding="8" HorizontalAlignment="Left" Margin="0,0,0,12">
                                <TextBlock Text="✅" FontSize="24"/>
                            </Border>
                            <TextBlock Text="مسددة بالكامل" FontWeight="Bold" FontSize="14" Foreground="#2E7D32" Margin="0,0,0,8"/>
                            <TextBlock x:Name="PaidInvoicesText" Text="0" FontSize="28" FontWeight="Bold" Foreground="#1B5E20"/>
                            <TextBlock Text="فاتورة" FontSize="13" Foreground="#546E7A"/>
                        </StackPanel>
                    </Border>

                    <!-- Total Amount Card -->
                    <Border Grid.Column="4" CornerRadius="16" Padding="24" Cursor="Hand">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#F3E5F5" Offset="0"/>
                                <GradientStop Color="#E1BEE7" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#9C27B0" Opacity="0.25" BlurRadius="12" ShadowDepth="4"/>
                        </Border.Effect>
                        <StackPanel>
                            <Border Background="White" CornerRadius="12" Padding="8" HorizontalAlignment="Left" Margin="0,0,0,12">
                                <TextBlock Text="💰" FontSize="24"/>
                            </Border>
                            <TextBlock Text="إجمالي المبلغ" FontWeight="Bold" FontSize="14" Foreground="#7B1FA2" Margin="0,0,0,8"/>
                            <TextBlock x:Name="TotalAmountText" Text="0" FontSize="28" FontWeight="Bold" Foreground="#4A148C"/>
                            <TextBlock Text="د.ع" FontSize="13" Foreground="#546E7A"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>

            <!-- Enhanced Search and Filter Section -->
            <Border Grid.Row="2" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#FFFFFF" Offset="0"/>
                        <GradientStop Color="#F8F9FA" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Enhanced Search Bar -->
                    <Grid Grid.Row="0" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Enhanced Search TextBox -->
                        <Border Grid.Column="0" CornerRadius="12" Margin="0,0,15,0">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="White" Offset="0"/>
                                    <GradientStop Color="#F8F9FA" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <Border.Effect>
                                <DropShadowEffect Color="#E0E0E0" Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <TextBox x:Name="SearchTextBox"
                                    Style="{StaticResource SearchBoxStyle}"
                                    TextChanged="SearchTextBox_TextChanged"
                                    BorderThickness="0"
                                    Background="Transparent"/>
                        </Border>

                        <!-- Enhanced Filter Button -->
                        <Button Grid.Column="1"
                                x:Name="FilterButton"
                                Style="{StaticResource ModernSecondaryButtonStyle}"
                                Click="FilterButton_Click"
                                Margin="0,0,12,0">
                            <StackPanel Orientation="Horizontal">
                                <Border Background="#E3F2FD" CornerRadius="6" Padding="4" Margin="0,0,8,0">
                                    <TextBlock Text="🎯" FontSize="14"/>
                                </Border>
                                <TextBlock Text="فلترة متقدمة" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Button>

                        <!-- Enhanced Refresh Button -->
                        <Button Grid.Column="2"
                                x:Name="RefreshButton"
                                Style="{StaticResource ModernIconButtonStyle}"
                                Click="RefreshButton_Click"
                                ToolTip="تحديث البيانات"
                                Margin="0,0,12,0">
                            <Border Background="#E8F5E8" CornerRadius="6" Padding="6">
                                <TextBlock Text="🔄" FontSize="16"/>
                            </Border>
                        </Button>

                        <!-- Quick Filter Buttons -->
                        <StackPanel Grid.Column="3" Orientation="Horizontal">
                            <Button x:Name="QuickFilterUnpaidButton"
                                    Style="{StaticResource ModernIconButtonStyle}"
                                    Click="QuickFilterUnpaid_Click"
                                    ToolTip="عرض غير المسددة فقط"
                                    Margin="0,0,8,0">
                                <Border Background="#FFEBEE" CornerRadius="6" Padding="6">
                                    <TextBlock Text="⏳" FontSize="14"/>
                                </Border>
                            </Button>
                            <Button x:Name="QuickFilterPaidButton"
                                    Style="{StaticResource ModernIconButtonStyle}"
                                    Click="QuickFilterPaid_Click"
                                    ToolTip="عرض المسددة فقط">
                                <Border Background="#E8F5E8" CornerRadius="6" Padding="6">
                                    <TextBlock Text="✅" FontSize="14"/>
                                </Border>
                            </Button>
                        </StackPanel>
                    </Grid>

                    <!-- Filter Panel -->
                    <Border x:Name="FilterPanel"
                            Grid.Row="1"
                            Background="#F8F9FA"
                            CornerRadius="8"
                            Padding="15"
                            Visibility="Collapsed">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Status Filter -->
                            <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                <TextBlock Text="الحالة" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="#495057"/>
                                <ComboBox x:Name="StatusFilterComboBox"
                                         Height="40"
                                         SelectionChanged="StatusFilterComboBox_SelectionChanged">
                                    <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                                    <ComboBoxItem Content="غير مسددة" Tag="Unpaid"/>
                                    <ComboBoxItem Content="مسددة جزئياً" Tag="PartiallyPaid"/>
                                    <ComboBoxItem Content="مسددة" Tag="Paid"/>
                                </ComboBox>
                            </StackPanel>

                            <!-- Date Range Filter -->
                            <StackPanel Grid.Column="1" Margin="0,0,15,0">
                                <TextBlock Text="من تاريخ" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="#495057"/>
                                <DatePicker x:Name="FromDatePicker"
                                           Height="40"
                                           SelectedDateChanged="DateFilter_Changed"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Margin="0,0,15,0">
                                <TextBlock Text="إلى تاريخ" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="#495057"/>
                                <DatePicker x:Name="ToDatePicker"
                                           Height="40"
                                           SelectedDateChanged="DateFilter_Changed"/>
                            </StackPanel>

                            <!-- Clear Filters Button -->
                            <Button Grid.Column="3"
                                    x:Name="ClearFiltersButton"
                                    Style="{StaticResource ModernSecondaryButtonStyle}"
                                    Click="ClearFiltersButton_Click"
                                    VerticalAlignment="Bottom">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🧹" FontSize="14" Margin="0,0,8,0"/>
                                    <TextBlock Text="مسح الفلاتر" FontWeight="Medium"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </Border>
                </Grid>
            </Border>



        <!-- Invoices List -->
        <Border Grid.Row="3" Style="{StaticResource ModernCardStyle}">
            <Grid>
                <!-- Modern DataGrid -->
                <DataGrid x:Name="InvoicesDataGrid"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        SelectionMode="Single"
                        GridLinesVisibility="None"
                        HeadersVisibility="Column"
                        Background="Transparent"
                        BorderThickness="0"
                        RowHeight="65"
                        FontSize="14"
                        AlternatingRowBackground="#FAFAFA"
                        MouseDoubleClick="InvoicesDataGrid_MouseDoubleClick"
                        EnableRowVirtualization="True"
                        EnableColumnVirtualization="True"
                        VirtualizingPanel.VirtualizationMode="Recycling"
                        VirtualizingPanel.IsVirtualizing="True"
                        ScrollViewer.CanContentScroll="True">

                    <!-- DataGrid Styles -->
                    <DataGrid.Resources>
                        <!-- Header Style -->
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F8F9FA"/>
                            <Setter Property="Foreground" Value="#495057"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="Padding" Value="15,12"/>
                            <Setter Property="BorderThickness" Value="0,0,0,2"/>
                            <Setter Property="BorderBrush" Value="#E9ECEF"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        </Style>

                        <!-- Row Style -->
                        <Style TargetType="DataGridRow">
                            <Setter Property="Background" Value="White"/>
                            <Setter Property="Margin" Value="0,2"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#BBDEFB"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>

                        <!-- Cell Style -->
                        <Style TargetType="DataGridCell">
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="15,8"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="DataGridCell">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DataGrid.Resources>

                    <DataGrid.Columns>
                        <!-- Invoice Number Column -->
                        <DataGridTemplateColumn Header="📋 رقم الفاتورة" Width="140">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="#E3F2FD" CornerRadius="6" Padding="8,4">
                                        <TextBlock Text="{Binding InvoiceNumber}"
                                                 FontWeight="Bold"
                                                 Foreground="#1976D2"
                                                 HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Supplier Column -->
                        <DataGridTemplateColumn Header="🏢 المورد" Width="180">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Border Background="#F3E5F5" CornerRadius="15" Width="30" Height="30" Margin="0,0,10,0">
                                            <TextBlock Text="{Binding SupplierName, Converter={StaticResource FirstLetterConverter}}"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     FontWeight="Bold"
                                                     Foreground="#7B1FA2"/>
                                        </Border>
                                        <TextBlock Text="{Binding SupplierName}"
                                                 VerticalAlignment="Center"
                                                 FontWeight="SemiBold"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Date Column -->
                        <DataGridTemplateColumn Header="📅 التاريخ" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding InvoiceDate, StringFormat=dd/MM/yyyy}"
                                             HorizontalAlignment="Center"
                                             Foreground="#6C757D"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Amount Column -->
                        <DataGridTemplateColumn Header="💰 المبلغ" Width="130">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding Amount, StringFormat=N0}"
                                                 FontWeight="Bold"
                                                 Foreground="#F57C00"/>
                                        <TextBlock Text="د.ع"
                                                 FontSize="10"
                                                 Foreground="#999"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Paid Amount Column -->
                        <DataGridTemplateColumn Header="💳 المبلغ المدفوع" Width="140">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding PaidAmount, StringFormat=N0}"
                                                 FontWeight="Bold"
                                                 Foreground="#388E3C"/>
                                        <TextBlock Text="د.ع"
                                                 FontSize="10"
                                                 Foreground="#999"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Remaining Amount Column -->
                        <DataGridTemplateColumn Header="⏰ المبلغ المتبقي" Width="140">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="8" Padding="8,4" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="Paid">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Unpaid">
                                                        <Setter Property="Background" Value="#FFEBEE"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="PartiallyPaid">
                                                        <Setter Property="Background" Value="#FFF3E0"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="PaidWithDiscount">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock FontWeight="Bold" HorizontalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="Paid">
                                                                <Setter Property="Text" Value="0"/>
                                                                <Setter Property="Foreground" Value="#388E3C"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="PaidWithDiscount">
                                                                <Setter Property="Text" Value="0"/>
                                                                <Setter Property="Foreground" Value="#388E3C"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="Unpaid">
                                                                <Setter Property="Text" Value="{Binding Amount, StringFormat=N0}"/>
                                                                <Setter Property="Foreground" Value="#D32F2F"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="PartiallyPaid">
                                                                <Setter Property="Text" Value="{Binding RemainingAmount, StringFormat=N0}"/>
                                                                <Setter Property="Foreground" Value="#F57C00"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                            <TextBlock Text="د.ع"
                                                     FontSize="10"
                                                     Foreground="#999"
                                                     HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Status Column -->
                        <DataGridTemplateColumn Header="📊 الحالة" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="Paid">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Unpaid">
                                                        <Setter Property="Background" Value="#FFEBEE"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="PartiallyPaid">
                                                        <Setter Property="Background" Value="#FFF3E0"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock FontWeight="Bold" HorizontalAlignment="Center">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="Paid">
                                                            <Setter Property="Text" Value="✅ مسددة"/>
                                                            <Setter Property="Foreground" Value="#388E3C"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Unpaid">
                                                            <Setter Property="Text" Value="⏳ غير مسددة"/>
                                                            <Setter Property="Foreground" Value="#D32F2F"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PartiallyPaid">
                                                            <Setter Property="Text" Value="⚡ جزئياً"/>
                                                            <Setter Property="Foreground" Value="#F57C00"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Description Column -->
                        <DataGridTextColumn Header="📝 الوصف" Binding="{Binding Description}" Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="#6C757D"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Attachments Column -->
                        <DataGridTemplateColumn Header="📎 المرفقات" Width="160">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <!-- Invoice Attachment Button -->
                                        <Button x:Name="ViewInvoiceAttachmentButton"
                                                Click="ViewInvoiceAttachment_Click"
                                                Tag="{Binding}"
                                                ToolTip="عرض مرفق الفاتورة"
                                                Margin="0,0,8,0"
                                                Width="32" Height="32">
                                            <Button.Style>
                                                <Style TargetType="Button" BasedOn="{StaticResource ModernIconButtonStyle}">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding AttachmentPath, Converter={StaticResource StringNullOrEmptyConverter}}" Value="True">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding AttachmentPath, Converter={StaticResource StringNullOrEmptyConverter}}" Value="False">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Button.Style>
                                            <Border Background="#E3F2FD" CornerRadius="6" Padding="4">
                                                <TextBlock Text="📄" FontSize="14" Foreground="#1976D2"/>
                                            </Border>
                                        </Button>

                                        <!-- Payment Receipt Attachment Button -->
                                        <Button x:Name="ViewReceiptAttachmentButton"
                                                Click="ViewReceiptAttachment_Click"
                                                Tag="{Binding}"
                                                ToolTip="عرض مرفق الوصل"
                                                Width="32" Height="32">
                                            <Button.Style>
                                                <Style TargetType="Button" BasedOn="{StaticResource ModernIconButtonStyle}">
                                                    <Style.Triggers>
                                                        <!-- Show only if invoice has payments and is not unpaid -->
                                                        <MultiDataTrigger>
                                                            <MultiDataTrigger.Conditions>
                                                                <Condition Binding="{Binding Status}" Value="Unpaid"/>
                                                            </MultiDataTrigger.Conditions>
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                        </MultiDataTrigger>
                                                        <MultiDataTrigger>
                                                            <MultiDataTrigger.Conditions>
                                                                <Condition Binding="{Binding Status}" Value="PartiallyPaid"/>
                                                            </MultiDataTrigger.Conditions>
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </MultiDataTrigger>
                                                        <MultiDataTrigger>
                                                            <MultiDataTrigger.Conditions>
                                                                <Condition Binding="{Binding Status}" Value="Paid"/>
                                                            </MultiDataTrigger.Conditions>
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </MultiDataTrigger>
                                                        <MultiDataTrigger>
                                                            <MultiDataTrigger.Conditions>
                                                                <Condition Binding="{Binding Status}" Value="PaidWithDiscount">
                                                                </Condition>
                                                            </MultiDataTrigger.Conditions>
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </MultiDataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Button.Style>
                                            <Border Background="#E8F5E8" CornerRadius="6" Padding="4">
                                                <TextBlock Text="🧾" FontSize="14" Foreground="#388E3C"/>
                                            </Border>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Loading Indicator -->
                <Border Background="White" CornerRadius="10" Visibility="Collapsed" x:Name="LoadingPanel">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border Background="#E3F2FD" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,20">
                            <TextBlock Text="⏳" FontSize="30" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="جاري تحميل الفواتير..."
                                 FontSize="16" FontWeight="SemiBold"
                                 Foreground="#495057" HorizontalAlignment="Center"/>
                        <ProgressBar Width="200" Height="4"
                                   IsIndeterminate="True"
                                   Margin="0,10,0,0"
                                   Background="#E9ECEF"
                                   Foreground="#007BFF"/>
                    </StackPanel>
                </Border>

                <!-- Empty State -->
                <Border x:Name="EmptyStatePanel"
                        Background="White"
                        CornerRadius="10"
                        Visibility="Collapsed">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Margin="40">
                        <Border Background="#F8F9FA" CornerRadius="50" Width="100" Height="100" Margin="0,0,0,20">
                            <TextBlock Text="📋" FontSize="40" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="لا توجد فواتير حالياً"
                                 FontSize="20" FontWeight="Bold"
                                 Foreground="#495057" HorizontalAlignment="Center"/>
                        <TextBlock Text="ابدأ بإضافة أول فاتورة لك"
                                 FontSize="14"
                                 Foreground="#6C757D" HorizontalAlignment="Center"
                                 Margin="0,8,0,20"/>
                        <Button Style="{StaticResource ModernPrimaryButtonStyle}"
                                Click="AddInvoiceButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="✨" FontSize="16" Margin="0,0,10,0"/>
                                <TextBlock Text="إضافة فاتورة جديدة" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
        </Grid>

        <!-- Invoice Form Overlay -->
        <controls:InvoiceFormOverlay x:Name="InvoiceFormOverlay"
                                   FormClosed="InvoiceFormOverlay_FormClosed"/>
    </Grid>
</UserControl>
