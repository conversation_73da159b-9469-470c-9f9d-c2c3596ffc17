using System;
using System.Globalization;

namespace HR_InvoiceArchiver.Utils
{
    /// <summary>
    /// مساعد تنسيق العملة للدينار العراقي
    /// </summary>
    public static class CurrencyHelper
    {
        /// <summary>
        /// رمز العملة - الدينار العراقي
        /// </summary>
        public const string CurrencySymbol = "د.ع";
        
        /// <summary>
        /// اسم العملة بالعربية
        /// </summary>
        public const string CurrencyName = "دينار عراقي";
        
        /// <summary>
        /// اسم العملة بالجمع
        /// </summary>
        public const string CurrencyNamePlural = "دينار عراقي";

        /// <summary>
        /// تنسيق المبلغ بالدينار العراقي
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="showDecimals">إظهار الكسور العشرية (افتراضي: true)</param>
        /// <returns>المبلغ منسق مع رمز العملة</returns>
        public static string FormatAmount(decimal amount, bool showDecimals = true)
        {
            if (showDecimals)
            {
                return $"{amount:N2} {CurrencySymbol}";
            }
            else
            {
                return $"{amount:N0} {CurrencySymbol}";
            }
        }

        /// <summary>
        /// تنسيق المبلغ بالدينار العراقي مع تنسيق مخصص
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="format">تنسيق الرقم (مثل: "N2", "F0", "C")</param>
        /// <returns>المبلغ منسق مع رمز العملة</returns>
        public static string FormatAmount(decimal amount, string format)
        {
            return $"{amount.ToString(format)} {CurrencySymbol}";
        }

        /// <summary>
        /// تنسيق المبلغ للعرض في DataGrid
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق للعرض في الجدول</returns>
        public static string FormatForGrid(decimal amount)
        {
            return FormatAmount(amount, false);
        }

        /// <summary>
        /// تنسيق المبلغ للعرض في الإحصائيات
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق للإحصائيات</returns>
        public static string FormatForStats(decimal amount)
        {
            return FormatAmount(amount, false);
        }

        /// <summary>
        /// تنسيق المبلغ للطباعة والتصدير
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق للطباعة</returns>
        public static string FormatForPrint(decimal amount)
        {
            return FormatAmount(amount, false);
        }

        /// <summary>
        /// الحصول على معلومات العملة الحالية
        /// </summary>
        /// <returns>معلومات العملة</returns>
        public static CurrencyInfo GetCurrencyInfo()
        {
            return new CurrencyInfo
            {
                Symbol = CurrencySymbol,
                Name = CurrencyName,
                NamePlural = CurrencyNamePlural,
                DecimalPlaces = 2,
                IsRightToLeft = true
            };
        }
    }

    /// <summary>
    /// معلومات العملة
    /// </summary>
    public class CurrencyInfo
    {
        public string Symbol { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string NamePlural { get; set; } = string.Empty;
        public int DecimalPlaces { get; set; }
        public bool IsRightToLeft { get; set; }
    }
}
