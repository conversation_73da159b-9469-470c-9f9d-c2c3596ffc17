using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Linq;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// أنواع الأخطاء
    /// </summary>
    public enum ErrorType
    {
        Unknown = 0,
        Database = 1,
        Network = 2,
        FileSystem = 3,
        Validation = 4,
        Business = 5,
        Security = 6,
        Performance = 7,
        UI = 8,
        CloudSync = 9
    }

    /// <summary>
    /// مستوى خطورة الخطأ
    /// </summary>
    public enum ErrorSeverity
    {
        Low = 0,
        Medium = 1,
        High = 2,
        Critical = 3
    }

    /// <summary>
    /// معلومات الخطأ المفصلة
    /// </summary>
    public class ErrorInfo
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public ErrorType Type { get; set; }
        public ErrorSeverity Severity { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Context { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public string? UserId { get; set; }
        public string? SessionId { get; set; }
        public string? SourceMethod { get; set; }
        public string? SourceFile { get; set; }
        public int? SourceLine { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();
        public bool IsResolved { get; set; } = false;
        public string? Resolution { get; set; }
        public DateTime? ResolvedAt { get; set; }
    }

    /// <summary>
    /// استراتيجية الاسترداد من الأخطاء
    /// </summary>
    public interface IRecoveryStrategy
    {
        Task<bool> TryRecoverAsync(ErrorInfo errorInfo);
        bool CanHandle(ErrorType errorType);
    }

    /// <summary>
    /// واجهة خدمة معالجة الأخطاء المحسنة
    /// </summary>
    public interface IEnhancedErrorHandlingService
    {
        /// <summary>
        /// معالجة استثناء
        /// </summary>
        Task HandleExceptionAsync(Exception exception, string context = "", 
            ErrorType errorType = ErrorType.Unknown, ErrorSeverity severity = ErrorSeverity.Medium,
            [CallerMemberName] string? callerMethod = null,
            [CallerFilePath] string? callerFile = null,
            [CallerLineNumber] int callerLine = 0);

        /// <summary>
        /// معالجة خطأ مخصص
        /// </summary>
        Task HandleErrorAsync(string message, ErrorType errorType = ErrorType.Unknown, 
            ErrorSeverity severity = ErrorSeverity.Medium, string context = "",
            Exception? exception = null, object? additionalData = null);

        /// <summary>
        /// الحصول على الأخطاء الحديثة
        /// </summary>
        Task<List<ErrorInfo>> GetRecentErrorsAsync(int count = 50);

        /// <summary>
        /// الحصول على إحصائيات الأخطاء
        /// </summary>
        Task<Dictionary<ErrorType, int>> GetErrorStatisticsAsync(DateTime? fromDate = null);

        /// <summary>
        /// تسجيل استراتيجية استرداد
        /// </summary>
        void RegisterRecoveryStrategy(IRecoveryStrategy strategy);

        /// <summary>
        /// تصدير تقرير الأخطاء
        /// </summary>
        Task<string> ExportErrorReportAsync(DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// وضع علامة على الخطأ كمحلول
        /// </summary>
        Task MarkErrorAsResolvedAsync(string errorId, string resolution);
    }

    /// <summary>
    /// تطبيق خدمة معالجة الأخطاء المحسنة
    /// </summary>
    public class EnhancedErrorHandlingService : IEnhancedErrorHandlingService
    {
        private readonly ILoggingService _loggingService;
        private readonly IToastService? _toastService;
        private readonly List<ErrorInfo> _errorHistory;
        private readonly List<IRecoveryStrategy> _recoveryStrategies;
        private readonly object _lockObject = new object();

        public EnhancedErrorHandlingService(ILoggingService loggingService, IToastService? toastService = null)
        {
            _loggingService = loggingService;
            _toastService = toastService;
            _errorHistory = new List<ErrorInfo>();
            _recoveryStrategies = new List<IRecoveryStrategy>();

            // تسجيل استراتيجيات الاسترداد الافتراضية
            RegisterDefaultRecoveryStrategies();
        }

        public async Task HandleExceptionAsync(Exception exception, string context = "", 
            ErrorType errorType = ErrorType.Unknown, ErrorSeverity severity = ErrorSeverity.Medium,
            [CallerMemberName] string? callerMethod = null,
            [CallerFilePath] string? callerFile = null,
            [CallerLineNumber] int callerLine = 0)
        {
            var errorInfo = new ErrorInfo
            {
                Type = errorType == ErrorType.Unknown ? DetermineErrorType(exception) : errorType,
                Severity = severity,
                Message = exception.Message,
                Context = context,
                Exception = exception,
                SourceMethod = callerMethod,
                SourceFile = System.IO.Path.GetFileName(callerFile),
                SourceLine = callerLine
            };

            await ProcessErrorAsync(errorInfo);
        }

        public async Task HandleErrorAsync(string message, ErrorType errorType = ErrorType.Unknown, 
            ErrorSeverity severity = ErrorSeverity.Medium, string context = "",
            Exception? exception = null, object? additionalData = null)
        {
            var errorInfo = new ErrorInfo
            {
                Type = errorType,
                Severity = severity,
                Message = message,
                Context = context,
                Exception = exception
            };

            if (additionalData != null)
            {
                errorInfo.AdditionalData["Data"] = additionalData;
            }

            await ProcessErrorAsync(errorInfo);
        }

        public async Task<List<ErrorInfo>> GetRecentErrorsAsync(int count = 50)
        {
            return await Task.FromResult(_errorHistory
                .OrderByDescending(e => e.Timestamp)
                .Take(count)
                .ToList());
        }

        public async Task<Dictionary<ErrorType, int>> GetErrorStatisticsAsync(DateTime? fromDate = null)
        {
            var filteredErrors = _errorHistory.AsEnumerable();
            
            if (fromDate.HasValue)
            {
                filteredErrors = filteredErrors.Where(e => e.Timestamp >= fromDate.Value);
            }

            return await Task.FromResult(filteredErrors
                .GroupBy(e => e.Type)
                .ToDictionary(g => g.Key, g => g.Count()));
        }

        public void RegisterRecoveryStrategy(IRecoveryStrategy strategy)
        {
            lock (_lockObject)
            {
                _recoveryStrategies.Add(strategy);
            }
        }

        public async Task<string> ExportErrorReportAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var filteredErrors = _errorHistory.AsEnumerable();
            
            if (fromDate.HasValue)
                filteredErrors = filteredErrors.Where(e => e.Timestamp >= fromDate.Value);
            
            if (toDate.HasValue)
                filteredErrors = filteredErrors.Where(e => e.Timestamp <= toDate.Value);

            var reportPath = System.IO.Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory, 
                "Reports", 
                $"error-report-{DateTime.Now:yyyy-MM-dd-HH-mm-ss}.csv");

            System.IO.Directory.CreateDirectory(System.IO.Path.GetDirectoryName(reportPath)!);

            using var writer = new System.IO.StreamWriter(reportPath);
            await writer.WriteLineAsync("Timestamp,Type,Severity,Message,Context,Exception,SourceMethod,SourceFile,SourceLine,IsResolved");
            
            foreach (var error in filteredErrors.OrderByDescending(e => e.Timestamp))
            {
                var line = $"{error.Timestamp:yyyy-MM-dd HH:mm:ss},{error.Type},{error.Severity}," +
                          $"\"{error.Message}\",\"{error.Context}\",\"{error.Exception?.Message}\"," +
                          $"{error.SourceMethod},{error.SourceFile},{error.SourceLine},{error.IsResolved}";
                await writer.WriteLineAsync(line);
            }

            return reportPath;
        }

        public async Task MarkErrorAsResolvedAsync(string errorId, string resolution)
        {
            lock (_lockObject)
            {
                var error = _errorHistory.FirstOrDefault(e => e.Id == errorId);
                if (error != null)
                {
                    error.IsResolved = true;
                    error.Resolution = resolution;
                    error.ResolvedAt = DateTime.Now;
                }
            }

            await _loggingService.LogInformationAsync($"تم حل الخطأ {errorId}: {resolution}");
        }

        private async Task ProcessErrorAsync(ErrorInfo errorInfo)
        {
            // إضافة الخطأ إلى التاريخ
            lock (_lockObject)
            {
                _errorHistory.Add(errorInfo);
                
                // الاحتفاظ بآخر 1000 خطأ فقط
                if (_errorHistory.Count > 1000)
                {
                    _errorHistory.RemoveAt(0);
                }
            }

            // تسجيل الخطأ
            await _loggingService.LogErrorAsync(errorInfo.Message, errorInfo.Exception, 
                MapErrorTypeToLogCategory(errorInfo.Type));

            // محاولة الاسترداد
            await TryRecoveryAsync(errorInfo);

            // إظهار رسالة للمستخدم حسب مستوى الخطورة
            await ShowUserNotificationAsync(errorInfo);
        }

        private async Task TryRecoveryAsync(ErrorInfo errorInfo)
        {
            var applicableStrategies = _recoveryStrategies
                .Where(s => s.CanHandle(errorInfo.Type))
                .ToList();

            foreach (var strategy in applicableStrategies)
            {
                try
                {
                    var recovered = await strategy.TryRecoverAsync(errorInfo);
                    if (recovered)
                    {
                        await MarkErrorAsResolvedAsync(errorInfo.Id, "تم الاسترداد تلقائياً");
                        break;
                    }
                }
                catch (Exception ex)
                {
                    await _loggingService.LogErrorAsync($"فشل في استراتيجية الاسترداد: {ex.Message}", ex);
                }
            }
        }

        private async Task ShowUserNotificationAsync(ErrorInfo errorInfo)
        {
            var userMessage = GetUserFriendlyMessage(errorInfo);

            switch (errorInfo.Severity)
            {
                case ErrorSeverity.Critical:
                    Application.Current?.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show(userMessage, "خطأ حرج", MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                    break;

                case ErrorSeverity.High:
                    _toastService?.ShowError("خطأ", userMessage);
                    break;

                case ErrorSeverity.Medium:
                    _toastService?.ShowWarning("تحذير", userMessage);
                    break;

                case ErrorSeverity.Low:
                    await _loggingService.LogWarningAsync(userMessage, MapErrorTypeToLogCategory(errorInfo.Type));
                    break;
            }
        }

        private string GetUserFriendlyMessage(ErrorInfo errorInfo)
        {
            return errorInfo.Type switch
            {
                ErrorType.Database => "حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى.",
                ErrorType.Network => "مشكلة في الاتصال بالشبكة. تحقق من اتصال الإنترنت.",
                ErrorType.FileSystem => "مشكلة في الوصول إلى الملفات. تحقق من الصلاحيات.",
                ErrorType.Validation => "البيانات المدخلة غير صحيحة. يرجى المراجعة.",
                ErrorType.Business => "خطأ في منطق العمل. يرجى مراجعة العملية.",
                ErrorType.Security => "مشكلة أمنية. يرجى التواصل مع المسؤول.",
                ErrorType.CloudSync => "مشكلة في المزامنة السحابية. سيتم المحاولة لاحقاً.",
                _ => !string.IsNullOrEmpty(errorInfo.Context) 
                    ? $"{errorInfo.Context}: {errorInfo.Message}"
                    : errorInfo.Message
            };
        }

        private ErrorType DetermineErrorType(Exception exception)
        {
            return exception switch
            {
                System.Data.Common.DbException => ErrorType.Database,
                Microsoft.EntityFrameworkCore.DbUpdateException => ErrorType.Database,
                System.Net.NetworkInformation.NetworkInformationException => ErrorType.Network,
                System.Net.Http.HttpRequestException => ErrorType.Network,
                System.IO.DirectoryNotFoundException => ErrorType.FileSystem,
                System.IO.FileNotFoundException => ErrorType.FileSystem,
                System.IO.IOException => ErrorType.FileSystem,
                System.UnauthorizedAccessException => ErrorType.Security,
                System.Security.SecurityException => ErrorType.Security,
                ArgumentNullException => ErrorType.Validation,
                ArgumentException => ErrorType.Validation,
                InvalidOperationException => ErrorType.Business,
                _ => ErrorType.Unknown
            };
        }

        private LogCategory MapErrorTypeToLogCategory(ErrorType errorType)
        {
            return errorType switch
            {
                ErrorType.Database => LogCategory.Database,
                ErrorType.Network => LogCategory.CloudSync,
                ErrorType.Security => LogCategory.Security,
                ErrorType.Performance => LogCategory.Performance,
                ErrorType.UI => LogCategory.UI,
                ErrorType.Business => LogCategory.Business,
                ErrorType.CloudSync => LogCategory.CloudSync,
                ErrorType.Validation => LogCategory.Business,
                _ => LogCategory.General
            };
        }

        private void RegisterDefaultRecoveryStrategies()
        {
            // يمكن إضافة استراتيجيات الاسترداد الافتراضية هنا
            RegisterRecoveryStrategy(new DatabaseRecoveryStrategy());
            RegisterRecoveryStrategy(new NetworkRecoveryStrategy());
        }
    }

    /// <summary>
    /// استراتيجية استرداد قاعدة البيانات
    /// </summary>
    public class DatabaseRecoveryStrategy : IRecoveryStrategy
    {
        public bool CanHandle(ErrorType errorType) => errorType == ErrorType.Database;

        public async Task<bool> TryRecoverAsync(ErrorInfo errorInfo)
        {
            // محاولة إعادة الاتصال بقاعدة البيانات
            await Task.Delay(1000); // محاكاة محاولة الاسترداد
            return false; // في التطبيق الحقيقي، نحاول الاسترداد الفعلي
        }
    }

    /// <summary>
    /// استراتيجية استرداد الشبكة
    /// </summary>
    public class NetworkRecoveryStrategy : IRecoveryStrategy
    {
        public bool CanHandle(ErrorType errorType) => errorType == ErrorType.Network;

        public async Task<bool> TryRecoverAsync(ErrorInfo errorInfo)
        {
            // محاولة إعادة الاتصال بالشبكة
            await Task.Delay(2000); // محاكاة محاولة الاسترداد
            return false; // في التطبيق الحقيقي، نحاول الاسترداد الفعلي
        }
    }
}
