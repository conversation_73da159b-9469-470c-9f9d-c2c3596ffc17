using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using System.Windows.Shapes;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Controls
{
    public partial class SupplierFormControl : UserControl, INotifyPropertyChanged
    {
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        
        public event PropertyChangedEventHandler? PropertyChanged;
        public event EventHandler<SupplierFormEventArgs>? FormClosed;
        
        private Supplier? _currentSupplier;
        private bool _isEditMode = false;
        
        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                _isEditMode = value;
                UpdateFormTitle();
                DeleteButton.Visibility = value ? Visibility.Visible : Visibility.Collapsed;
                OnPropertyChanged(nameof(IsEditMode));
            }
        }
        
        public Supplier? CurrentSupplier
        {
            get => _currentSupplier;
            set
            {
                _currentSupplier = value;
                IsEditMode = value != null;
                PopulateForm();
                OnPropertyChanged(nameof(CurrentSupplier));
            }
        }
        
        public SupplierFormControl()
        {
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
            
            InitializeComponent();
            DataContext = this;
            
            Loaded += SupplierFormControl_Loaded;
        }
        
        private void SupplierFormControl_Loaded(object sender, RoutedEventArgs e)
        {
            // Start slide-in animation
            var slideInStoryboard = (Storyboard)FindResource("SlideInAnimation");
            slideInStoryboard.Begin();
            
            // Focus on first input
            NameTextBox.Focus();
        }
        
        private void UpdateFormTitle()
        {
            FormTitleTextBlock.Text = IsEditMode ? "تعديل بيانات المورد" : "إضافة مورد جديد";
        }
        
        private void PopulateForm()
        {
            if (CurrentSupplier == null)
            {
                ClearForm();
                return;
            }
            
            NameTextBox.Text = CurrentSupplier.Name ?? string.Empty;
            ContactPersonTextBox.Text = CurrentSupplier.ContactPerson ?? string.Empty;
            PhoneTextBox.Text = CurrentSupplier.Phone ?? string.Empty;
            EmailTextBox.Text = CurrentSupplier.Email ?? string.Empty;
            AddressTextBox.Text = CurrentSupplier.Address ?? string.Empty;
            NotesTextBox.Text = CurrentSupplier.Notes ?? string.Empty;
        }
        
        private void ClearForm()
        {
            NameTextBox.Clear();
            ContactPersonTextBox.Clear();
            PhoneTextBox.Clear();
            EmailTextBox.Clear();
            AddressTextBox.Clear();
            NotesTextBox.Clear();
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                _toastService.ShowError("خطأ في التحقق", "يرجى إدخال اسم المورد");
                NameTextBox.Focus();
                return false;
            }
            
            // Validate email format if provided
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(EmailTextBox.Text);
                    if (addr.Address != EmailTextBox.Text)
                    {
                        _toastService.ShowError("خطأ في التحقق", "تنسيق البريد الإلكتروني غير صحيح");
                        EmailTextBox.Focus();
                        return false;
                    }
                }
                catch
                {
                    _toastService.ShowError("خطأ في التحقق", "تنسيق البريد الإلكتروني غير صحيح");
                    EmailTextBox.Focus();
                    return false;
                }
            }
            
            return true;
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;
            
            try
            {
                SaveButton.IsEnabled = false;
                
                // Play success animation
                var successAnimation = (Storyboard)FindResource("SaveSuccessAnimation");
                successAnimation.Begin();
                
                var supplier = CurrentSupplier ?? new Supplier();
                
                supplier.Name = NameTextBox.Text.Trim();
                supplier.ContactPerson = ContactPersonTextBox.Text.Trim();
                supplier.Phone = PhoneTextBox.Text.Trim();
                supplier.Email = EmailTextBox.Text.Trim();
                supplier.Address = AddressTextBox.Text.Trim();
                supplier.Notes = NotesTextBox.Text.Trim();
                
                if (IsEditMode)
                {
                    await _supplierService.UpdateSupplierAsync(supplier);
                    _toastService.ShowSuccess("تم التحديث بنجاح", $"تم تحديث بيانات المورد '{supplier.Name}' بنجاح");
                }
                else
                {
                    supplier.CreatedDate = DateTime.Now;
                    await _supplierService.CreateSupplierAsync(supplier);
                    _toastService.ShowSuccess("تم الحفظ بنجاح", $"تم إضافة المورد '{supplier.Name}' بنجاح");
                }
                
                // Wait a moment for animation to complete
                await Task.Delay(300);
                
                // Close form with success result
                FormClosed?.Invoke(this, new SupplierFormEventArgs(supplier, true));
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في الحفظ", $"حدث خطأ أثناء حفظ بيانات المورد: {ex.Message}");
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            CloseForm();
        }
        
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentSupplier == null) return;
            
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المورد '{CurrentSupplier.Name}'؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا المورد.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning,
                MessageBoxResult.No);
            
            if (result != MessageBoxResult.Yes) return;
            
            try
            {
                DeleteButton.IsEnabled = false;
                
                await _supplierService.DeleteSupplierAsync(CurrentSupplier.Id);
                _toastService.ShowSuccess("تم الحذف بنجاح", $"تم حذف المورد '{CurrentSupplier.Name}' بنجاح");
                
                // Close form with delete result
                FormClosed?.Invoke(this, new SupplierFormEventArgs(CurrentSupplier, true, true));
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في الحذف", $"حدث خطأ أثناء حذف المورد: {ex.Message}");
            }
            finally
            {
                DeleteButton.IsEnabled = true;
            }
        }
        
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            CloseForm();
        }

        private void BackgroundGrid_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // Check if the click was on the background Grid or Rectangle (not on the card)
            if (e.Source == sender || e.OriginalSource is Rectangle)
            {
                CloseForm();
            }
        }

        private void CloseForm()
        {
            // Start slide-out animation
            var slideOutStoryboard = (Storyboard)FindResource("SlideOutAnimation");
            slideOutStoryboard.Begin();
        }
        
        private void SlideOutAnimation_Completed(object sender, EventArgs e)
        {
            // Close form with cancel result
            FormClosed?.Invoke(this, new SupplierFormEventArgs(null, false));
        }
        
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
    
    public class SupplierFormEventArgs : EventArgs
    {
        public Supplier? Supplier { get; }
        public bool Success { get; }
        public bool IsDeleted { get; }
        
        public SupplierFormEventArgs(Supplier? supplier, bool success, bool isDeleted = false)
        {
            Supplier = supplier;
            Success = success;
            IsDeleted = isDeleted;
        }
    }
}
