<UserControl x:Class="HR_InvoiceArchiver.Controls.PaymentFormControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent"
             MinWidth="550" MaxWidth="800" MinHeight="600" MaxHeight="900"
             HorizontalAlignment="Stretch"
             VerticalAlignment="Stretch">

    <UserControl.Resources>
        <!-- Modern Purple-Pink Gradient Theme -->
        <LinearGradientBrush x:Key="PaymentGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#8E24AA" Offset="0"/>
            <GradientStop Color="#E91E63" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#9C27B0" Offset="0"/>
            <GradientStop Color="#673AB7" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="AccentGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF4081" Offset="0"/>
            <GradientStop Color="#E91E63" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Background" Value="{StaticResource PaymentGradient}"/>
            <Setter Property="BorderBrush" Value="{StaticResource PaymentGradient}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="24,12"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#9C27B0" BlurRadius="8" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#9C27B0" BlurRadius="12" ShadowDepth="3" Opacity="0.4"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="materialDesign:HintAssist.Foreground" Value="#9C27B0"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource PaymentGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextBoxViewMargin" Value="0,0,0,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
        </Style>

        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="materialDesign:HintAssist.Foreground" Value="#9C27B0"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource PaymentGradient}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
        </Style>

        <!-- Enhanced Slide In Animation -->
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                           From="400" To="0" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.4"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.4"/>
        </Storyboard>
        
        <!-- Enhanced Slide Out Animation -->
        <Storyboard x:Key="SlideOutAnimation" Completed="SlideOutAnimation_Completed">
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                           From="0" To="400" Duration="0:0:0.4">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseIn" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.3"/>
        </Storyboard>
        
        <!-- Save Success Animation -->
        <Storyboard x:Key="SaveSuccessAnimation">
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                           From="1" To="1.05" Duration="0:0:0.2" AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                           From="1" To="1.05" Duration="0:0:0.2" AutoReverse="True"/>
        </Storyboard>
        
        <!-- Modern Card Style -->
        <Style x:Key="ModernPaymentCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" BlurRadius="20" ShadowDepth="8" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Modern Input Style -->
        <Style x:Key="ModernInputStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource PaymentGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>
        
        <!-- Enhanced ComboBox Style -->
        <Style x:Key="EnhancedComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="materialDesign:HintAssist.Foreground" Value="#9C27B0"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource PaymentGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>

        <!-- Enhanced TextBox Style -->
        <Style x:Key="EnhancedTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="materialDesign:HintAssist.Foreground" Value="#9C27B0"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource PaymentGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>

        <!-- Enhanced DatePicker Style -->
        <Style x:Key="EnhancedDatePickerStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="materialDesign:HintAssist.Foreground" Value="#9C27B0"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource PaymentGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>

        <!-- Section Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource HeaderGradient}"/>
            <Setter Property="CornerRadius" Value="8,8,0,0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#9C27B0" BlurRadius="8" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Section Content Style -->
        <Style x:Key="SectionContentStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="0,0,8,8"/>
            <Setter Property="Padding" Value="25,20"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1,0,1,1"/>
        </Style>


    </UserControl.Resources>

    <!-- Main Container -->
    <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch">

        <!-- Main Payment Card -->
        <Border x:Name="MainCard" Background="White" CornerRadius="20"
                HorizontalAlignment="Center" VerticalAlignment="Center"
                MinWidth="550" MaxWidth="800" MinHeight="600" MaxHeight="900"
                Margin="20">

            <Border.Effect>
                <DropShadowEffect Color="#9C27B0" BlurRadius="20" ShadowDepth="5" Opacity="0.3"/>
            </Border.Effect>

            <Border.RenderTransform>
                <TransformGroup>
                    <TranslateTransform/>
                    <ScaleTransform/>
                </TransformGroup>
            </Border.RenderTransform>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Beautiful Header with Gradient -->
                <Border Grid.Row="0" Background="{StaticResource HeaderGradient}"
                       CornerRadius="20,20,0,0" Padding="30,25">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Receipt" Width="32" Height="32"
                                                   Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                            <StackPanel>
                                <TextBlock Text="إضافة وصل دفع جديد" FontSize="24" FontWeight="Bold" Foreground="White"/>
                                <TextBlock Text="تسجيل دفعة جديدة للفاتورة المحددة" FontSize="14" Foreground="#E0E0E0" Margin="0,4,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Button Grid.Column="1" x:Name="CloseButton"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Width="40" Height="40" Padding="8"
                               Foreground="White" Click="CloseButton_Click">
                            <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                        </Button>
                    </Grid>
                </Border>

                <!-- Form Content -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                             Padding="30,25" Background="White">
                    <StackPanel>

                        <!-- Invoice Selection Section -->
                        <Border Background="#F8F4FF" CornerRadius="16" Padding="25" Margin="0,0,0,25">
                            <Border.Effect>
                                <DropShadowEffect Color="#E1BEE7" BlurRadius="8" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>
                            <StackPanel>
                                <Grid Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="{StaticResource AccentGradient}"
                                           CornerRadius="12" Width="40" Height="40" Margin="0,0,15,0">
                                        <materialDesign:PackIcon Kind="FileDocumentOutline" Width="20" Height="20"
                                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>

                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                        <TextBlock Text="اختيار الفاتورة" FontSize="18" FontWeight="Bold" Foreground="#9C27B0"/>
                                        <TextBlock Text="حدد الفاتورة المراد إضافة دفعة لها" FontSize="13" Foreground="#666666" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </Grid>

                                <ComboBox x:Name="InvoiceComboBox"
                                         Style="{StaticResource EnhancedComboBoxStyle}"
                                         materialDesign:HintAssist.Hint="اختر الفاتورة المراد دفعها أو ابحث..."
                                         DisplayMemberPath="DisplayText"
                                         SelectedValuePath="Id"
                                         FontSize="14" Padding="16,12"
                                         IsEditable="True"
                                         IsTextSearchEnabled="True"
                                         StaysOpenOnEdit="True"
                                         TextSearch.TextPath="DisplayText"
                                         SelectionChanged="InvoiceComboBox_SelectionChanged"
                                         PreviewKeyDown="InvoiceComboBox_PreviewKeyDown"
                                         DropDownOpened="InvoiceComboBox_DropDownOpened"
                                         KeyUp="InvoiceComboBox_KeyUp"/>

                                <!-- Invoice Info Panel -->
                                <Border x:Name="InvoiceInfoPanel"
                                       Background="White"
                                       CornerRadius="12"
                                       Padding="20"
                                       Margin="0,15,0,0"
                                       Visibility="Collapsed">
                                    <Border.Effect>
                                        <DropShadowEffect Color="#E1BEE7" BlurRadius="6" ShadowDepth="1" Opacity="0.2"/>
                                    </Border.Effect>
                                    <StackPanel>
                                        <TextBlock Text="📋 معلومات الفاتورة"
                                                  FontWeight="SemiBold" FontSize="15"
                                                  Foreground="#9C27B0"
                                                  Margin="0,0,0,8"/>
                                        <TextBlock x:Name="InvoiceInfoTextBlock" 
                                                  TextWrapping="Wrap" 
                                                  Foreground="#424242"
                                                  LineHeight="20"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Border>

                        <!-- Payment Details Section -->
                        <StackPanel>
                            <!-- Section Header -->
                            <Border Style="{StaticResource SectionHeaderStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CashMultiple" Width="24" Height="24"
                                                           Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <StackPanel>
                                        <TextBlock Text="تفاصيل الدفعة" FontSize="18" FontWeight="Bold" Foreground="White"/>
                                        <TextBlock Text="أدخل معلومات الدفعة والوصل" FontSize="12" Foreground="#E0E0E0" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Section Content -->
                            <Border Style="{StaticResource SectionContentStyle}">
                                <StackPanel>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="10"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="10"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Receipt Number -->
                                        <TextBox x:Name="ReceiptNumberTextBox" Grid.Column="0"
                                                Style="{StaticResource EnhancedTextBoxStyle}"
                                                materialDesign:HintAssist.Hint="رقم الوصل *"/>

                                        <!-- Payment Status (Moved before Payment Amount) -->
                                        <ComboBox x:Name="PaymentStatusComboBox" Grid.Column="2"
                                                 Style="{StaticResource EnhancedComboBoxStyle}"
                                                 materialDesign:HintAssist.Hint="حالة التسديد *"
                                                 SelectionChanged="PaymentStatusComboBox_SelectionChanged">
                                            <ComboBoxItem Content="تسديد جزئي" Tag="PartiallyPaid" Foreground="#FF9800"/>
                                            <ComboBoxItem Content="تسديد كامل" Tag="FullyPaid" Foreground="#4CAF50"/>
                                            <ComboBoxItem Content="تسديد وبخصم" Tag="PaymentWithDiscount" Foreground="#9C27B0"/>
                                        </ComboBox>

                                        <!-- Payment Amount (Moved after Payment Status) -->
                                        <TextBox x:Name="PaymentAmountTextBox" Grid.Column="4"
                                                Style="{StaticResource EnhancedTextBoxStyle}"
                                                materialDesign:HintAssist.Hint="مبلغ الدفعة (د.ع) *"
                                                PreviewTextInput="PaymentAmountTextBox_PreviewTextInput"
                                                TextChanged="PaymentAmountTextBox_TextChanged"/>
                                    </Grid>

                                    <!-- Smart Status Indicator -->
                                    <Border x:Name="StatusIndicatorPanel" Background="White" CornerRadius="8" Padding="12" Margin="0,0,0,15" Visibility="Collapsed">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#E0E0E0" BlurRadius="4" ShadowDepth="1" Opacity="0.3"/>
                                        </Border.Effect>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon x:Name="StatusIcon" Kind="Information" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock x:Name="StatusMessageText" FontSize="12" FontWeight="SemiBold"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Payment Date -->
                                    <DatePicker x:Name="PaymentDatePicker"
                                               Style="{StaticResource EnhancedDatePickerStyle}"
                                               materialDesign:HintAssist.Hint="تاريخ الدفع *"/>

                                    <!-- Discount Amount (Visible only when PaymentWithDiscount is selected) -->
                                    <TextBox x:Name="DiscountAmountTextBox"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="مبلغ الخصم (د.ع)"
                                            PreviewTextInput="PaymentAmountTextBox_PreviewTextInput"
                                            TextChanged="DiscountAmountTextBox_TextChanged"
                                            Visibility="Collapsed"
                                            Margin="0,15,0,0"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>

                        <!-- Payment Method & Notes Section -->
                        <StackPanel>
                            <!-- Section Header -->
                            <Border Style="{StaticResource SectionHeaderStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CreditCardOutline" Width="24" Height="24"
                                                           Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <StackPanel>
                                        <TextBlock Text="طريقة الدفع والملاحظات" FontSize="18" FontWeight="Bold" Foreground="White"/>
                                        <TextBlock Text="حدد طريقة الدفع وأضف ملاحظات إضافية" FontSize="12" Foreground="#E0E0E0" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Section Content -->
                            <Border Style="{StaticResource SectionContentStyle}">

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="15"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Payment Method -->
                                    <ComboBox x:Name="PaymentMethodComboBox" Grid.Column="0"
                                             Style="{StaticResource EnhancedComboBoxStyle}"
                                             materialDesign:HintAssist.Hint="طريقة الدفع *"
                                             SelectedIndex="0">
                                            <ComboBoxItem Content="💵 نقدي" Tag="Cash"/>
                                            <ComboBoxItem Content="💳 بطاقة بنكية" Tag="Card"/>
                                        </ComboBox>

                                    <!-- Notes -->
                                    <TextBox x:Name="NotesTextBox" Grid.Column="2"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="ملاحظات إضافية"
                                            Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                            VerticalScrollBarVisibility="Auto"/>
                                </Grid>
                            </Border>
                        </StackPanel>

                        <!-- File Attachment Section -->
                        <StackPanel>
                            <!-- Section Header -->
                            <Border Style="{StaticResource SectionHeaderStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Attachment" Width="24" Height="24"
                                                           Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <StackPanel>
                                        <TextBlock Text="مرفق الوصل" FontSize="18" FontWeight="Bold" Foreground="White"/>
                                        <TextBlock Text="أرفق صورة أو ملف الوصل (اختياري)" FontSize="12" Foreground="#E0E0E0" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Section Content -->
                            <Border Style="{StaticResource SectionContentStyle}">

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="15"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox x:Name="AttachmentPathTextBox" Grid.Column="0"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="مسار الملف المرفق"
                                            IsReadOnly="True"/>

                                    <Button Grid.Column="2" Click="BrowseAttachmentButton_Click"
                                           Style="{StaticResource MaterialDesignRaisedButton}"
                                           Background="{StaticResource PaymentGradient}" Foreground="White"
                                           Height="56" Padding="20,0" FontWeight="SemiBold"
                                           materialDesign:ButtonAssist.CornerRadius="25"
                                           materialDesign:ElevationAssist.Elevation="Dp4">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FolderOpen" Width="18" Height="18" Margin="0,0,8,0"/>
                                            <TextBlock Text="تصفح" FontSize="14"/>
                                        </StackPanel>
                                    </Button>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>

                <!-- Beautiful Action Buttons -->
                <Border Grid.Row="2" Background="#F8F9FA"
                       CornerRadius="0,0,20,20" Padding="30,25" MinHeight="80">
                    <Border.Effect>
                        <DropShadowEffect Color="#E0E0E0" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                    </Border.Effect>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="SaveButton" Click="SaveButton_Click"
                               Style="{StaticResource ModernPrimaryButtonStyle}"
                               Background="{StaticResource PaymentGradient}" Foreground="White"
                               MinWidth="140" Height="56" Margin="0,0,20,0"
                               FontSize="14" FontWeight="SemiBold" Padding="24,16"
                               materialDesign:ButtonAssist.CornerRadius="25"
                               materialDesign:ElevationAssist.Elevation="Dp4">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="18" Height="18" Margin="0,0,10,0"/>
                                <TextBlock x:Name="SaveButtonText" Text="حفظ الوصل"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="CancelButton" Click="CancelButton_Click"
                               Style="{StaticResource ModernSecondaryButtonStyle}"
                               BorderBrush="#9C27B0" Foreground="#9C27B0"
                               FontWeight="SemiBold" MinWidth="140" Height="56"
                               FontSize="14" Padding="24,16"
                               materialDesign:ButtonAssist.CornerRadius="25">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cancel" Width="18" Height="18" Margin="0,0,10,0"/>
                                <TextBlock Text="إلغاء"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </Grid>
</UserControl>
