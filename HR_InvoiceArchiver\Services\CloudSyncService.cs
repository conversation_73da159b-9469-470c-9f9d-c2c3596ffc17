using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Utils;
using Microsoft.EntityFrameworkCore;
using Timer = System.Timers.Timer;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة المزامنة التلقائية مع التخزين السحابي
    /// </summary>
    public class CloudSyncService : IDisposable
    {
        private readonly ICloudStorageService _cloudService;
        private readonly DatabaseContext _context;
        private readonly IToastService? _toastService;
        private readonly Timer _syncTimer;
        private readonly SemaphoreSlim _syncSemaphore;
        private bool _isEnabled;
        private bool _isSyncing;

        // إعدادات المزامنة
        private readonly TimeSpan _syncInterval = TimeSpan.FromMinutes(30); // كل 30 دقيقة
        private readonly TimeSpan _retryDelay = TimeSpan.FromMinutes(5);

        public event EventHandler<SyncProgressEventArgs>? SyncProgress;
        public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;

        public bool IsEnabled => _isEnabled;
        public bool IsSyncing => _isSyncing;
        public DateTime? LastSyncTime { get; private set; }

        public CloudSyncService(
            ICloudStorageService cloudService,
            DatabaseContext context,
            IToastService? toastService = null)
        {
            _cloudService = cloudService;
            _context = context;
            _toastService = toastService;
            _syncSemaphore = new SemaphoreSlim(1, 1);

            // إعداد المؤقت
            _syncTimer = new Timer(_syncInterval.TotalMilliseconds);
            _syncTimer.Elapsed += OnSyncTimerElapsed;
            _syncTimer.AutoReset = true;
        }

        /// <summary>
        /// بدء خدمة المزامنة التلقائية
        /// </summary>
        public async Task StartAsync()
        {
            try
            {
                // التحقق من الاتصال بالسحابة
                var isConnected = await _cloudService.IsConnectedAsync();
                if (!isConnected)
                {
                    _toastService?.ShowWarning("تحذير", "لم يتم ربط التخزين السحابي. المزامنة التلقائية معطلة.");
                    return;
                }

                _isEnabled = true;
                _syncTimer.Start();
                
                _toastService?.ShowInfo("المزامنة", "تم تفعيل المزامنة التلقائية");

                // تشغيل مزامنة فورية
                _ = Task.Run(SyncNowAsync);
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في بدء خدمة المزامنة: {ex.Message}");
            }
        }

        /// <summary>
        /// إيقاف خدمة المزامنة التلقائية
        /// </summary>
        public void Stop()
        {
            _isEnabled = false;
            _syncTimer.Stop();
            _toastService?.ShowInfo("المزامنة", "تم إيقاف المزامنة التلقائية");
        }

        /// <summary>
        /// مزامنة فورية
        /// </summary>
        public async Task<SyncResult> SyncNowAsync()
        {
            if (!await _syncSemaphore.WaitAsync(TimeSpan.FromSeconds(5)))
            {
                return new SyncResult 
                { 
                    Success = false, 
                    Errors = new List<string> { "المزامنة قيد التشغيل بالفعل" } 
                };
            }

            try
            {
                _isSyncing = true;
                SyncProgress?.Invoke(this, new SyncProgressEventArgs { Message = "بدء المزامنة..." });

                var result = await PerformSyncAsync();
                
                LastSyncTime = DateTime.Now;
                
                SyncCompleted?.Invoke(this, new SyncCompletedEventArgs { Result = result });
                
                return result;
            }
            finally
            {
                _isSyncing = false;
                _syncSemaphore.Release();
            }
        }

        /// <summary>
        /// تنفيذ عملية المزامنة
        /// </summary>
        private async Task<SyncResult> PerformSyncAsync()
        {
            var result = new SyncResult();
            var startTime = DateTime.Now;

            try
            {
                // التحقق من الاتصال
                if (!await _cloudService.IsConnectedAsync())
                {
                    result.Errors.Add("لا يوجد اتصال بالتخزين السحابي");
                    return result;
                }

                // مزامنة مرفقات المدفوعات
                SyncProgress?.Invoke(this, new SyncProgressEventArgs { Message = "مزامنة مرفقات المدفوعات..." });
                var paymentResult = await SyncPaymentAttachmentsAsync();
                result.FilesUploaded += paymentResult.FilesUploaded;
                result.FilesSkipped += paymentResult.FilesSkipped;
                result.Errors.AddRange(paymentResult.Errors);

                // مزامنة مرفقات الفواتير
                SyncProgress?.Invoke(this, new SyncProgressEventArgs { Message = "مزامنة مرفقات الفواتير..." });
                var invoiceResult = await SyncInvoiceAttachmentsAsync();
                result.FilesUploaded += invoiceResult.FilesUploaded;
                result.FilesSkipped += invoiceResult.FilesSkipped;
                result.Errors.AddRange(invoiceResult.Errors);

                result.Success = result.Errors.Count == 0;
                
                if (result.Success)
                {
                    _toastService?.ShowSuccess("المزامنة", 
                        $"تمت المزامنة بنجاح. تم رفع {result.FilesUploaded} ملف.");
                }
                else
                {
                    _toastService?.ShowWarning("المزامنة", 
                        $"تمت المزامنة مع {result.Errors.Count} خطأ.");
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Errors.Add($"خطأ عام في المزامنة: {ex.Message}");
                _toastService?.ShowError("خطأ في المزامنة", ex.Message);
            }

            result.Duration = DateTime.Now - startTime;
            return result;
        }

        /// <summary>
        /// مزامنة مرفقات المدفوعات
        /// </summary>
        private async Task<SyncResult> SyncPaymentAttachmentsAsync()
        {
            var result = new SyncResult();

            try
            {
                // الحصول على المدفوعات التي تحتاج مزامنة
                var paymentsToSync = await _context.Payments
                    .Where(p => p.IsActive && 
                               !string.IsNullOrEmpty(p.AttachmentPath) &&
                               (p.SyncStatus == CloudSyncStatus.Pending || p.SyncStatus == CloudSyncStatus.Failed))
                    .ToListAsync();

                foreach (var payment in paymentsToSync)
                {
                    try
                    {
                        await SyncPaymentAttachmentAsync(payment);
                        result.FilesUploaded++;
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"فشل في مزامنة مرفق الوصل {payment.ReceiptNumber}: {ex.Message}");
                        
                        // تحديث حالة المزامنة إلى فشل
                        payment.SyncStatus = CloudSyncStatus.Failed;
                        payment.UpdatedDate = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في مزامنة مرفقات المدفوعات: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// مزامنة مرفقات الفواتير
        /// </summary>
        private async Task<SyncResult> SyncInvoiceAttachmentsAsync()
        {
            var result = new SyncResult();

            try
            {
                // الحصول على الفواتير التي تحتاج مزامنة
                var invoicesToSync = await _context.Invoices
                    .Where(i => i.IsActive && 
                               !string.IsNullOrEmpty(i.AttachmentPath) &&
                               (i.SyncStatus == CloudSyncStatus.Pending || i.SyncStatus == CloudSyncStatus.Failed))
                    .ToListAsync();

                foreach (var invoice in invoicesToSync)
                {
                    try
                    {
                        await SyncInvoiceAttachmentAsync(invoice);
                        result.FilesUploaded++;
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"فشل في مزامنة مرفق الفاتورة {invoice.InvoiceNumber}: {ex.Message}");
                        
                        // تحديث حالة المزامنة إلى فشل
                        invoice.SyncStatus = CloudSyncStatus.Failed;
                        invoice.UpdatedDate = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في مزامنة مرفقات الفواتير: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// مزامنة مرفق مدفوعة واحدة
        /// </summary>
        private async Task SyncPaymentAttachmentAsync(Payment payment)
        {
            if (string.IsNullOrEmpty(payment.AttachmentPath))
                return;

            // تحديث حالة المزامنة إلى جاري المزامنة
            payment.SyncStatus = CloudSyncStatus.Syncing;
            await _context.SaveChangesAsync();

            var localPath = FileHelper.GetFullAttachmentPath(payment.AttachmentPath, "Payments");
            
            if (!File.Exists(localPath))
            {
                throw new FileNotFoundException("الملف المحلي غير موجود");
            }

            // تحديد مسار السحابة
            var cloudPath = $"Payments/{DateTime.Now.Year}/{DateTime.Now.Month:D2}";
            var cloudFileName = $"PAY_{payment.ReceiptNumber}_{Path.GetFileName(payment.AttachmentPath)}";

            // رفع الملف
            var cloudFileId = await _cloudService.UploadFileAsync(localPath, cloudFileName, cloudPath);
            
            if (string.IsNullOrEmpty(cloudFileId))
            {
                throw new Exception("فشل في رفع الملف إلى السحابة");
            }

            // تحديث معلومات المزامنة
            payment.CloudFileId = cloudFileId;
            payment.SyncStatus = CloudSyncStatus.Synced;
            payment.LastSyncDate = DateTime.Now;
            payment.UpdatedDate = DateTime.Now;
        }

        /// <summary>
        /// مزامنة مرفق فاتورة واحدة
        /// </summary>
        private async Task SyncInvoiceAttachmentAsync(Invoice invoice)
        {
            if (string.IsNullOrEmpty(invoice.AttachmentPath))
                return;

            // تحديث حالة المزامنة إلى جاري المزامنة
            invoice.SyncStatus = CloudSyncStatus.Syncing;
            await _context.SaveChangesAsync();

            var localPath = FileHelper.GetFullAttachmentPath(invoice.AttachmentPath, "Invoices");
            
            if (!File.Exists(localPath))
            {
                throw new FileNotFoundException("الملف المحلي غير موجود");
            }

            // تحديد مسار السحابة
            var cloudPath = $"Invoices/{DateTime.Now.Year}/{DateTime.Now.Month:D2}";
            var cloudFileName = $"INV_{invoice.InvoiceNumber}_{Path.GetFileName(invoice.AttachmentPath)}";

            // رفع الملف
            var cloudFileId = await _cloudService.UploadFileAsync(localPath, cloudFileName, cloudPath);
            
            if (string.IsNullOrEmpty(cloudFileId))
            {
                throw new Exception("فشل في رفع الملف إلى السحابة");
            }

            // تحديث معلومات المزامنة
            invoice.CloudFileId = cloudFileId;
            invoice.SyncStatus = CloudSyncStatus.Synced;
            invoice.LastSyncDate = DateTime.Now;
            invoice.UpdatedDate = DateTime.Now;
        }

        /// <summary>
        /// معالج حدث المؤقت
        /// </summary>
        private async void OnSyncTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            if (!_isEnabled || _isSyncing)
                return;

            try
            {
                await SyncNowAsync();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في المزامنة التلقائية", ex.Message);
            }
        }

        public void Dispose()
        {
            Stop();
            _syncTimer?.Dispose();
            _syncSemaphore?.Dispose();
        }
    }

    /// <summary>
    /// أحداث تقدم المزامنة
    /// </summary>
    public class SyncProgressEventArgs : EventArgs
    {
        public string Message { get; set; } = string.Empty;
        public int Progress { get; set; }
        public int Total { get; set; }
    }

    /// <summary>
    /// أحداث اكتمال المزامنة
    /// </summary>
    public class SyncCompletedEventArgs : EventArgs
    {
        public SyncResult Result { get; set; } = new();
    }
}
