using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Utils;
using HR_InvoiceArchiver.Data.Repositories;
using LiveCharts;
using LiveCharts.Wpf;
using LiveCharts.Defaults;
using ClosedXML.Excel;

namespace HR_InvoiceArchiver.Windows
{
    public partial class SearchWindow : Window, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        
        public event PropertyChangedEventHandler? PropertyChanged;
        
        public ObservableCollection<Invoice> SearchResults { get; set; } = new();
        public ObservableCollection<Supplier> Suppliers { get; set; } = new();

        private int _currentPage = 1;
        private int _pageSize = 50;
        private int _totalPages = 1;
        private int _totalRecords = 0;

        // Statistics Properties
        private int _totalInvoices;
        private decimal _totalAmount;
        private decimal _paidAmount;
        private decimal _outstandingAmount;

        public int TotalInvoices
        {
            get => _totalInvoices;
            set { _totalInvoices = value; OnPropertyChanged(nameof(TotalInvoices)); }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set { _totalAmount = value; OnPropertyChanged(nameof(TotalAmount)); }
        }

        public decimal PaidAmount
        {
            get => _paidAmount;
            set { _paidAmount = value; OnPropertyChanged(nameof(PaidAmount)); }
        }

        public decimal OutstandingAmount
        {
            get => _outstandingAmount;
            set { _outstandingAmount = value; OnPropertyChanged(nameof(OutstandingAmount)); }
        }

        // Chart Properties
        public SeriesCollection StatusChartSeries { get; set; } = new();
        
        private decimal? _minAmount;
        public decimal? MinAmount
        {
            get => _minAmount;
            set
            {
                _minAmount = value;
                OnPropertyChanged(nameof(MinAmount));
            }
        }
        
        private decimal? _maxAmount;
        public decimal? MaxAmount
        {
            get => _maxAmount;
            set
            {
                _maxAmount = value;
                OnPropertyChanged(nameof(MaxAmount));
            }
        }
        
        public SearchWindow()
        {
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();

            InitializeComponent();

            // Initialize collections
            SearchResults = new ObservableCollection<Invoice>();
            Suppliers = new ObservableCollection<Supplier>();

            DataContext = this;

            InitializeChart();
            Loaded += SearchWindow_Loaded;
        }
        
        private async void SearchWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSuppliersAsync();
            InitializeFilters();
            await LoadStatisticsAsync();
        }
        
        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersBasicAsync(); // استخدام الطريقة المحسنة

                // Ensure UI updates happen on UI thread
                await Dispatcher.InvokeAsync(() =>
                {
                    Suppliers.Clear();
                    Suppliers.Add(new Supplier { Id = 0, Name = "جميع الموردين" });

                    foreach (var supplier in suppliers)
                    {
                        Suppliers.Add(supplier);
                    }

                    SupplierComboBox.ItemsSource = Suppliers;
                    SupplierComboBox.SelectedIndex = 0;
                });


            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل الموردين", $"حدث خطأ: {ex.Message}");
            }
        }
        
        private void InitializeFilters()
        {
            StatusComboBox.SelectedIndex = 0; // All statuses
            
            // Set default date range (last 3 months)
            ToDatePicker.SelectedDate = DateTime.Today;
            FromDatePicker.SelectedDate = DateTime.Today.AddMonths(-3);
        }
        
        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformSearchAsync();
        }
        
        private async Task PerformSearchAsync()
        {
            try
            {
                SearchButton.IsEnabled = false;

                var filter = CreateSearchFilter();
                var results = await _invoiceService.GetFilteredInvoicesAsync(filter);

                // Apply pagination
                _totalRecords = results.Count();
                _totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
                _currentPage = 1;

                var pagedResults = results.Skip((_currentPage - 1) * _pageSize).Take(_pageSize);

                SearchResults.Clear();
                foreach (var invoice in pagedResults)
                {
                    SearchResults.Add(invoice);
                }

                ResultsDataGrid.ItemsSource = SearchResults;
                UpdateResultsInfo();
                UpdatePaginationButtons();

                // Update statistics with search results
                UpdateSearchStatistics(results);

                _toastService.ShowSuccess("نتائج البحث", $"تم العثور على {_totalRecords} فاتورة");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في البحث", $"حدث خطأ: {ex.Message}");
            }
            finally
            {
                SearchButton.IsEnabled = true;
            }
        }
        
        private InvoiceFilter CreateSearchFilter()
        {
            var filter = new InvoiceFilter();
            
            // Invoice number
            if (!string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
                filter.InvoiceNumber = InvoiceNumberTextBox.Text.Trim();
            
            // Supplier
            if (SupplierComboBox.SelectedValue != null && (int)SupplierComboBox.SelectedValue > 0)
                filter.SupplierId = (int)SupplierComboBox.SelectedValue;
            
            // Date range
            if (FromDatePicker.SelectedDate.HasValue)
                filter.FromDate = FromDatePicker.SelectedDate.Value;
            
            if (ToDatePicker.SelectedDate.HasValue)
                filter.ToDate = ToDatePicker.SelectedDate.Value.AddDays(1).AddSeconds(-1); // End of day
            
            // Due date range
            if (DueFromDatePicker.SelectedDate.HasValue)
                filter.DueFromDate = DueFromDatePicker.SelectedDate.Value;
            
            if (DueToDatePicker.SelectedDate.HasValue)
                filter.DueToDate = DueToDatePicker.SelectedDate.Value.AddDays(1).AddSeconds(-1);
            
            // Amount range
            if (MinAmount.HasValue)
                filter.MinAmount = MinAmount.Value;
            
            if (MaxAmount.HasValue)
                filter.MaxAmount = MaxAmount.Value;
            
            // Status
            if (StatusComboBox.SelectedItem is ComboBoxItem statusItem && statusItem.Tag?.ToString() != "All")
            {
                if (Enum.TryParse<InvoiceStatus>(statusItem.Tag?.ToString(), out var status))
                    filter.Status = status;
            }
            
            // Overdue only
            filter.OverdueOnly = OverdueOnlyCheckBox.IsChecked == true;
            
            return filter;
        }
        
        private void UpdateResultsInfo()
        {
            ResultCountTextBlock.Text = $"{_totalRecords} فاتورة";
            PageInfoTextBlock.Text = $"صفحة {_currentPage} من {_totalPages}";
        }
        
        private void UpdatePaginationButtons()
        {
            FirstPageButton.IsEnabled = _currentPage > 1;
            PrevPageButton.IsEnabled = _currentPage > 1;
            NextPageButton.IsEnabled = _currentPage < _totalPages;
            LastPageButton.IsEnabled = _currentPage < _totalPages;
        }
        
        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            // Clear all filters
            InvoiceNumberTextBox.Text = string.Empty;
            SupplierComboBox.SelectedIndex = 0;
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            DueFromDatePicker.SelectedDate = null;
            DueToDatePicker.SelectedDate = null;
            MinAmountTextBox.Text = string.Empty;
            MaxAmountTextBox.Text = string.Empty;
            StatusComboBox.SelectedIndex = 0;
            OverdueOnlyCheckBox.IsChecked = false;
            
            // Clear results
            SearchResults.Clear();
            ResultCountTextBlock.Text = "0 فاتورة";
            PageInfoTextBlock.Text = "صفحة 1 من 1";
            
            // Filters cleared
        }
        
        private async void FirstPageButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = 1;
            await LoadCurrentPageAsync();
        }
        
        private async void PrevPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadCurrentPageAsync();
            }
        }
        
        private async void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                _currentPage++;
                await LoadCurrentPageAsync();
            }
        }
        
        private async void LastPageButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = _totalPages;
            await LoadCurrentPageAsync();
        }
        
        private async Task LoadCurrentPageAsync()
        {
            try
            {
                // Loading page...
                
                var filter = CreateSearchFilter();
                var results = await _invoiceService.GetFilteredInvoicesAsync(filter);
                
                var pagedResults = results.Skip((_currentPage - 1) * _pageSize).Take(_pageSize);
                
                SearchResults.Clear();
                foreach (var invoice in pagedResults)
                {
                    SearchResults.Add(invoice);
                }
                
                UpdateResultsInfo();
                UpdatePaginationButtons();
                
                // Page loaded successfully
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل الصفحة", $"حدث خطأ: {ex.Message}");
            }
        }
        
        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!SearchResults.Any())
                {
                    _toastService.ShowWarning("تنبيه", "لا توجد نتائج للتصدير");
                    return;
                }

                // Get all results (not just current page)
                var filter = CreateSearchFilter();
                var allResults = await _invoiceService.GetFilteredInvoicesAsync(filter);

                var fileName = $"نتائج_البحث_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                await ExportHelper.ExportInvoicesToExcelAsync(allResults, fileName);

                _toastService.ShowSuccess("نجح التصدير", $"تم تصدير النتائج بنجاح إلى: {fileName}");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التصدير", $"حدث خطأ: {ex.Message}");
            }
        }
        
        private void ResultsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Could add functionality to view selected invoice details
        }
        
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #region Statistics and Charts

        private void InitializeChart()
        {
            try
            {
                if (StatusPieChart != null)
                {
                    StatusPieChart.Series = StatusChartSeries;
                    StatusPieChart.DataContext = this;
                    System.Diagnostics.Debug.WriteLine("Chart initialized successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("StatusPieChart is null!");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing chart: {ex.Message}");
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                var allInvoices = await _invoiceService.GetAllInvoicesBasicAsync(); // استخدام الطريقة المحسنة

                // Calculate statistics
                TotalInvoices = allInvoices.Count();
                TotalAmount = allInvoices.Sum(i => i.Amount);
                PaidAmount = allInvoices.Sum(i => i.PaidAmount);
                OutstandingAmount = TotalAmount - PaidAmount;



                // Update UI
                UpdateStatisticsDisplay();

                // Update chart with delay to ensure UI is ready
                await Task.Delay(100);
                UpdateStatusChart(allInvoices);


            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل الإحصائيات", $"حدث خطأ: {ex.Message}");

                // Set default values on error
                TotalInvoices = 0;
                TotalAmount = 0;
                PaidAmount = 0;
                OutstandingAmount = 0;
            }
        }

        private void UpdateStatisticsDisplay()
        {
            // Properties will automatically update UI through data binding
            // No need for manual UI updates
        }

        private void UpdateSearchStatistics(IEnumerable<Invoice> searchResults)
        {
            // Update statistics based on search results
            TotalInvoices = searchResults.Count();
            TotalAmount = searchResults.Sum(i => i.Amount);
            PaidAmount = searchResults.Sum(i => i.PaidAmount);
            OutstandingAmount = TotalAmount - PaidAmount;

            // Update UI
            UpdateStatisticsDisplay();
            UpdateStatusChart(searchResults);
        }

        private void UpdateStatusChart(IEnumerable<Invoice> invoices)
        {
            try
            {
                StatusChartSeries.Clear();

                var statusGroups = invoices.GroupBy(i => i.Status)
                                         .Select(g => new { Status = g.Key, Count = g.Count() })
                                         .ToList();

                System.Diagnostics.Debug.WriteLine($"Status groups count: {statusGroups.Count}");

                if (!statusGroups.Any())
                {
                    if (NoDataPanel != null) NoDataPanel.Visibility = Visibility.Visible;
                    if (StatusPieChart != null) StatusPieChart.Visibility = Visibility.Collapsed;
                    return;
                }

                if (NoDataPanel != null) NoDataPanel.Visibility = Visibility.Collapsed;
                if (StatusPieChart != null) StatusPieChart.Visibility = Visibility.Visible;

                foreach (var group in statusGroups)
                {
                    var statusName = GetStatusDisplayName(group.Status);
                    var statusColor = GetStatusColor(group.Status);

                    StatusChartSeries.Add(new PieSeries
                    {
                        Title = $"{statusName} ({group.Count})",
                        Values = new ChartValues<ObservableValue> { new ObservableValue(group.Count) },
                        Fill = new System.Windows.Media.SolidColorBrush(statusColor),
                        DataLabels = true,
                        LabelPoint = chartPoint => $"{chartPoint.Y} ({chartPoint.Participation:P})"
                    });

                    System.Diagnostics.Debug.WriteLine($"Added series: {statusName} = {group.Count}");
                }

                // Force chart refresh
                if (StatusPieChart != null)
                {
                    StatusPieChart.Update(true, true);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating chart: {ex.Message}");
                _toastService.ShowError("خطأ في تحديث المخطط", ex.Message);
            }
        }

        private string GetStatusDisplayName(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Unpaid => "غير مسددة",
                InvoiceStatus.PartiallyPaid => "مسددة جزئياً",
                InvoiceStatus.Paid => "مسددة",
                _ => status.ToString()
            };
        }

        private System.Windows.Media.Color GetStatusColor(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Unpaid => System.Windows.Media.Colors.Red,
                InvoiceStatus.PartiallyPaid => System.Windows.Media.Colors.Orange,
                InvoiceStatus.Paid => System.Windows.Media.Colors.Green,
                _ => System.Windows.Media.Colors.Gray
            };
        }

        #endregion
    }
}
