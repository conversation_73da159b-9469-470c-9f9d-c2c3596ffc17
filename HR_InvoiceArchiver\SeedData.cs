using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Models;
using Microsoft.EntityFrameworkCore;

namespace HR_InvoiceArchiver
{
    public static class SeedData
    {
        public static async Task SeedDatabaseAsync(DatabaseContext context)
        {
            // Check if data already exists - محسن للأداء
            if (await context.Invoices.AsNoTracking().AnyAsync())
                return;

            // تحسين الأداء أثناء Seeding
            context.ChangeTracker.AutoDetectChangesEnabled = false;

            // Create sample suppliers
            var suppliers = new List<Supplier>
            {
                new Supplier
                {
                    Name = "شركة الأمل للتجارة",
                    ContactPerson = "أحمد محمد",
                    Phone = "07901234567",
                    Email = "<EMAIL>",
                    Address = "بغداد - الكرادة",
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new Supplier
                {
                    Name = "مؤسسة النور للمواد الغذائية",
                    ContactPerson = "فاطمة علي",
                    Phone = "07801234567",
                    Email = "<EMAIL>",
                    Address = "البصرة - المعقل",
                    CreatedDate = DateTime.Now.AddDays(-25)
                },
                new Supplier
                {
                    Name = "شركة الفرات للخدمات",
                    ContactPerson = "محمد حسن",
                    Phone = "07701234567",
                    Email = "<EMAIL>",
                    Address = "النجف - المركز",
                    CreatedDate = DateTime.Now.AddDays(-20)
                }
            };

            await context.Suppliers.AddRangeAsync(suppliers);
            await context.SaveChangesAsync();

            // Create sample invoices
            var invoices = new List<Invoice>
            {
                new Invoice
                {
                    InvoiceNumber = "INV-2024-001",
                    SupplierId = suppliers[0].Id,
                    InvoiceDate = DateTime.Now.AddDays(-15),
                    DueDate = DateTime.Now.AddDays(15),
                    Amount = 1500000,
                    PaidAmount = 1500000,
                    Status = InvoiceStatus.Paid,
                    Description = "توريد مواد مكتبية",
                    CreatedDate = DateTime.Now.AddDays(-15)
                },
                new Invoice
                {
                    InvoiceNumber = "INV-2024-002",
                    SupplierId = suppliers[1].Id,
                    InvoiceDate = DateTime.Now.AddDays(-10),
                    DueDate = DateTime.Now.AddDays(20),
                    Amount = 2500000,
                    PaidAmount = 1000000,
                    Status = InvoiceStatus.PartiallyPaid,
                    Description = "توريد مواد غذائية",
                    CreatedDate = DateTime.Now.AddDays(-10)
                },
                new Invoice
                {
                    InvoiceNumber = "INV-2024-003",
                    SupplierId = suppliers[2].Id,
                    InvoiceDate = DateTime.Now.AddDays(-5),
                    DueDate = DateTime.Now.AddDays(25),
                    Amount = 3000000,
                    PaidAmount = 0,
                    Status = InvoiceStatus.Unpaid,
                    Description = "خدمات صيانة",
                    CreatedDate = DateTime.Now.AddDays(-5)
                },
                new Invoice
                {
                    InvoiceNumber = "INV-2024-004",
                    SupplierId = suppliers[0].Id,
                    InvoiceDate = DateTime.Now.AddDays(-45),
                    DueDate = DateTime.Now.AddDays(-15),
                    Amount = 1800000,
                    PaidAmount = 0,
                    Status = InvoiceStatus.Unpaid,
                    Description = "توريد أجهزة كمبيوتر",
                    CreatedDate = DateTime.Now.AddDays(-45)
                },
                new Invoice
                {
                    InvoiceNumber = "INV-2024-005",
                    SupplierId = suppliers[1].Id,
                    InvoiceDate = DateTime.Now.AddDays(-3),
                    DueDate = DateTime.Now.AddDays(27),
                    Amount = 4200000,
                    PaidAmount = 4200000,
                    Status = InvoiceStatus.Paid,
                    Description = "توريد معدات مطبخ",
                    CreatedDate = DateTime.Now.AddDays(-3)
                }
            };

            await context.Invoices.AddRangeAsync(invoices);
            await context.SaveChangesAsync();

            // Create sample payments
            var payments = new List<Payment>
            {
                new Payment
                {
                    InvoiceId = invoices[0].Id,
                    Amount = 1500000,
                    PaymentDate = DateTime.Now.AddDays(-10),
                    Method = PaymentMethod.Cash,
                    ReceiptNumber = "REC-001",
                    Notes = "دفع كامل",
                    CreatedDate = DateTime.Now.AddDays(-10)
                },
                new Payment
                {
                    InvoiceId = invoices[1].Id,
                    Amount = 1000000,
                    PaymentDate = DateTime.Now.AddDays(-5),
                    Method = PaymentMethod.CreditCard,
                    ReceiptNumber = "REC-002",
                    Notes = "دفع جزئي",
                    CreatedDate = DateTime.Now.AddDays(-5)
                },
                new Payment
                {
                    InvoiceId = invoices[4].Id,
                    Amount = 4200000,
                    PaymentDate = DateTime.Now.AddDays(-1),
                    Method = PaymentMethod.Cash,
                    ReceiptNumber = "REC-003",
                    Notes = "دفع كامل",
                    CreatedDate = DateTime.Now.AddDays(-1)
                }
            };

            await context.Payments.AddRangeAsync(payments);

            // حفظ جميع التغييرات مرة واحدة لتحسين الأداء
            await context.SaveChangesAsync();

            // إعادة تفعيل AutoDetectChanges
            context.ChangeTracker.AutoDetectChangesEnabled = true;

            System.Diagnostics.Debug.WriteLine("Sample data seeded successfully with performance optimizations");
        }
    }
}
