using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HR_InvoiceArchiver.Tests.Models
{
    public class PaymentTests
    {
        [Fact]
        public void Payment_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var payment = new Payment();

            // Assert
            payment.ReceiptNumber.Should().Be(string.Empty);
            payment.Amount.Should().Be(0);
            payment.DiscountAmount.Should().Be(0);
            payment.RefundValue.Should().Be(0);
            payment.PaymentDate.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
            payment.Method.Should().Be(PaymentMethod.Cash);
            payment.Status.Should().Be(PaymentStatus.FullPayment);
            payment.SyncStatus.Should().Be(CloudSyncStatus.Pending);
            payment.CreatedDate.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
            payment.IsActive.Should().BeTrue();
        }

        [Theory]
        [InlineData(PaymentMethod.Cash, "نقدي")]
        [InlineData(PaymentMethod.Check, "شيك")]
        [InlineData(PaymentMethod.BankTransfer, "تحويل بنكي")]
        [InlineData(PaymentMethod.CreditCard, "بطاقة ائتمان")]
        [InlineData(PaymentMethod.Other, "أخرى")]
        public void Payment_MethodText_ShouldReturnCorrectArabicText(PaymentMethod method, string expectedText)
        {
            // Arrange
            var payment = new Payment { Method = method };

            // Act
            var methodText = payment.MethodText;

            // Assert
            methodText.Should().Be(expectedText);
        }

        [Theory]
        [InlineData(PaymentStatus.FullPayment, "تسديد كامل")]
        [InlineData(PaymentStatus.PartialPayment, "تسديد جزئي")]
        [InlineData(PaymentStatus.PaymentWithDiscount, "تسديد وبخصم")]
        [InlineData(PaymentStatus.PaymentWithRefund, "تسديد واسترجاع المتبقي")]
        public void Payment_StatusText_ShouldReturnCorrectArabicText(PaymentStatus status, string expectedText)
        {
            // Arrange
            var payment = new Payment { Status = status };

            // Act
            var statusText = payment.StatusText;

            // Assert
            statusText.Should().Be(expectedText);
        }

        [Theory]
        [InlineData(CloudSyncStatus.Pending, "في انتظار المزامنة")]
        [InlineData(CloudSyncStatus.Syncing, "جاري المزامنة")]
        [InlineData(CloudSyncStatus.Synced, "تمت المزامنة")]
        [InlineData(CloudSyncStatus.Failed, "فشلت المزامنة")]
        [InlineData(CloudSyncStatus.Disabled, "المزامنة معطلة")]
        public void Payment_SyncStatusText_ShouldReturnCorrectArabicText(CloudSyncStatus syncStatus, string expectedText)
        {
            // Arrange
            var payment = new Payment { SyncStatus = syncStatus };

            // Act
            var syncStatusText = payment.SyncStatusText;

            // Assert
            syncStatusText.Should().Be(expectedText);
        }

        [Fact]
        public void Payment_TotalSettlement_ShouldCalculateCorrectly()
        {
            // Arrange
            var payment = new Payment
            {
                Amount = 1000,
                RefundValue = 200
            };

            // Act
            var totalSettlement = payment.TotalSettlement;

            // Assert
            totalSettlement.Should().Be(1200); // 1000 + 200
        }

        [Fact]
        public void Payment_HasCloudBackup_WithCloudFileIdAndSynced_ShouldReturnTrue()
        {
            // Arrange
            var payment = new Payment
            {
                CloudFileId = "file123",
                SyncStatus = CloudSyncStatus.Synced
            };

            // Act
            var hasCloudBackup = payment.HasCloudBackup;

            // Assert
            hasCloudBackup.Should().BeTrue();
        }

        [Fact]
        public void Payment_HasCloudBackup_WithoutCloudFileId_ShouldReturnFalse()
        {
            // Arrange
            var payment = new Payment
            {
                CloudFileId = null,
                SyncStatus = CloudSyncStatus.Synced
            };

            // Act
            var hasCloudBackup = payment.HasCloudBackup;

            // Assert
            hasCloudBackup.Should().BeFalse();
        }

        [Fact]
        public void Payment_HasCloudBackup_WithCloudFileIdButNotSynced_ShouldReturnFalse()
        {
            // Arrange
            var payment = new Payment
            {
                CloudFileId = "file123",
                SyncStatus = CloudSyncStatus.Pending
            };

            // Act
            var hasCloudBackup = payment.HasCloudBackup;

            // Assert
            hasCloudBackup.Should().BeFalse();
        }

        [Fact]
        public void Payment_SupplierName_WithInvoiceAndSupplier_ShouldReturnSupplierName()
        {
            // Arrange
            var payment = new Payment
            {
                Invoice = new Invoice
                {
                    Supplier = new Supplier { Name = "مورد تجريبي" }
                }
            };

            // Act
            var supplierName = payment.SupplierName;

            // Assert
            supplierName.Should().Be("مورد تجريبي");
        }

        [Fact]
        public void Payment_SupplierName_WithNullInvoice_ShouldReturnEmpty()
        {
            // Arrange
            var payment = new Payment { Invoice = null! };

            // Act
            var supplierName = payment.SupplierName;

            // Assert
            supplierName.Should().Be(string.Empty);
        }

        [Fact]
        public void Payment_InvoiceNumber_WithInvoice_ShouldReturnInvoiceNumber()
        {
            // Arrange
            var payment = new Payment
            {
                Invoice = new Invoice { InvoiceNumber = "INV-001" }
            };

            // Act
            var invoiceNumber = payment.InvoiceNumber;

            // Assert
            invoiceNumber.Should().Be("INV-001");
        }

        [Fact]
        public void Payment_InvoiceNumber_WithNullInvoice_ShouldReturnEmpty()
        {
            // Arrange
            var payment = new Payment { Invoice = null! };

            // Act
            var invoiceNumber = payment.InvoiceNumber;

            // Assert
            invoiceNumber.Should().Be(string.Empty);
        }

        [Fact]
        public void Payment_IsValidAmount_WithValidAmount_ShouldReturnTrue()
        {
            // Arrange
            var payment = new Payment
            {
                Amount = 500,
                Invoice = new Invoice
                {
                    Amount = 1000,
                    Payments = new List<Payment>
                    {
                        new Payment { Amount = 300, IsActive = true }
                    }
                }
            };

            // Act
            var isValid = payment.IsValidAmount();

            // Assert
            isValid.Should().BeTrue(); // 500 <= 700 (remaining amount)
        }

        [Fact]
        public void Payment_IsValidAmount_WithAmountExceedingRemaining_ShouldReturnFalse()
        {
            // Arrange
            var payment = new Payment
            {
                Amount = 800,
                Invoice = new Invoice
                {
                    Amount = 1000,
                    Payments = new List<Payment>
                    {
                        new Payment { Amount = 300, IsActive = true }
                    }
                }
            };

            // Act
            var isValid = payment.IsValidAmount();

            // Assert
            isValid.Should().BeFalse(); // 800 > 700 (remaining amount)
        }

        [Fact]
        public void Payment_IsValidAmount_WithZeroAmount_ShouldReturnFalse()
        {
            // Arrange
            var payment = new Payment
            {
                Amount = 0,
                Invoice = new Invoice { Amount = 1000 }
            };

            // Act
            var isValid = payment.IsValidAmount();

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public void Payment_IsUniqueReceiptNumber_WithUniqueNumber_ShouldReturnTrue()
        {
            // Arrange
            var payment = new Payment
            {
                Id = 1,
                ReceiptNumber = "REC-001"
            };

            var existingPayments = new List<Payment>
            {
                new Payment { Id = 2, ReceiptNumber = "REC-002", IsActive = true },
                new Payment { Id = 3, ReceiptNumber = "REC-003", IsActive = true }
            };

            // Act
            var isUnique = payment.IsUniqueReceiptNumber(existingPayments);

            // Assert
            isUnique.Should().BeTrue();
        }

        [Fact]
        public void Payment_IsUniqueReceiptNumber_WithDuplicateNumber_ShouldReturnFalse()
        {
            // Arrange
            var payment = new Payment
            {
                Id = 1,
                ReceiptNumber = "REC-001"
            };

            var existingPayments = new List<Payment>
            {
                new Payment { Id = 2, ReceiptNumber = "REC-001", IsActive = true },
                new Payment { Id = 3, ReceiptNumber = "REC-003", IsActive = true }
            };

            // Act
            var isUnique = payment.IsUniqueReceiptNumber(existingPayments);

            // Assert
            isUnique.Should().BeFalse();
        }

        [Fact]
        public void Payment_IsUniqueReceiptNumber_WithSameIdAndNumber_ShouldReturnTrue()
        {
            // Arrange
            var payment = new Payment
            {
                Id = 1,
                ReceiptNumber = "REC-001"
            };

            var existingPayments = new List<Payment>
            {
                new Payment { Id = 1, ReceiptNumber = "REC-001", IsActive = true }, // نفس الـ ID
                new Payment { Id = 2, ReceiptNumber = "REC-002", IsActive = true }
            };

            // Act
            var isUnique = payment.IsUniqueReceiptNumber(existingPayments);

            // Assert
            isUnique.Should().BeTrue(); // يجب أن يكون صحيح لأنه نفس السجل
        }
    }
}
