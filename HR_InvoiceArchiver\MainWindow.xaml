<Window x:Class="HR_InvoiceArchiver.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام أرشفة الفواتير"
        Height="900" Width="1400"
        MinHeight="700" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        WindowStyle="SingleBorderWindow"
        AllowsTransparency="False"
        Background="#F8F9FA"
        FontFamily="Segoe UI, Tahoma, Arial Unicode MS"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True">

    <Window.Resources>
        <!-- Modern Professional Color Palette -->

        <!-- Primary Colors - Day Theme -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#2C3E50"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#007BFF"/>
        <SolidColorBrush x:Key="AccentColor" Color="#28A745"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="HeaderColor" Color="#FFFFFF"/>

        <!-- Sidebar Day Theme Gradient -->
        <LinearGradientBrush x:Key="SidebarGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#F0F8FF" Offset="0"/>
            <GradientStop Color="#E6F3FF" Offset="0.3"/>
            <GradientStop Color="#D4EDDA" Offset="0.7"/>
            <GradientStop Color="#CCE5FF" Offset="1"/>
        </LinearGradientBrush>

        <!-- Sidebar Text Colors -->
        <SolidColorBrush x:Key="SidebarTextColor" Color="#495057"/>
        <SolidColorBrush x:Key="SidebarIconColor" Color="#6C757D"/>

        <!-- Button Hover Colors -->
        <SolidColorBrush x:Key="ButtonHoverColor" Color="#0056B3"/>
        <LinearGradientBrush x:Key="ButtonHoverGradient" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#E3F2FD" Offset="0"/>
            <GradientStop Color="#BBDEFB" Offset="1"/>
        </LinearGradientBrush>

        <!-- Selected Button Gradient -->
        <LinearGradientBrush x:Key="SelectedButtonGradient" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#007BFF" Offset="0"/>
            <GradientStop Color="#0056B3" Offset="1"/>
        </LinearGradientBrush>

        <!-- Active Button Background -->
        <SolidColorBrush x:Key="ActiveButtonBackground" Color="#E3F2FD"/>

        <!-- Modern Navigation Button Style - Day Theme -->
        <Style x:Key="ModernNavButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="50"/>
            <Setter Property="Margin" Value="10,5"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Foreground" Value="{StaticResource SidebarTextColor}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="10"
                                Margin="{TemplateBinding Margin}">
                            <Border.RenderTransform>
                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                            </Border.RenderTransform>
                            <Border.RenderTransformOrigin>
                                <Point X="0.5" Y="0.5"/>
                            </Border.RenderTransformOrigin>
                            <StackPanel Orientation="Horizontal"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Margin="15,0">
                                <TextBlock Text="{TemplateBinding Content}"
                                           Foreground="{TemplateBinding Foreground}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontWeight="{TemplateBinding FontWeight}"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                                <TextBlock x:Name="IconText"
                                           Text="{TemplateBinding Tag}"
                                           FontSize="18"
                                           VerticalAlignment="Center"
                                           Foreground="{StaticResource SidebarIconColor}"/>
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource ButtonHoverGradient}"/>
                                <Setter Property="Foreground" Value="{StaticResource ButtonHoverColor}"/>
                                <Setter TargetName="IconText" Property="Foreground" Value="{StaticResource ButtonHoverColor}"/>
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                             To="1.02" Duration="0:0:0.15"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                             To="1.02" Duration="0:0:0.15"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                             To="1.0" Duration="0:0:0.15"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                             To="1.0" Duration="0:0:0.15"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Selected Button Style - Day Theme -->
        <Style x:Key="SelectedNavButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernNavButtonStyle}">
            <Setter Property="Background" Value="{StaticResource ActiveButtonBackground}"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="10"
                                Margin="{TemplateBinding Margin}"
                                BorderBrush="{StaticResource SecondaryColor}"
                                BorderThickness="2">
                            <Border.RenderTransform>
                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                            </Border.RenderTransform>
                            <Border.RenderTransformOrigin>
                                <Point X="0.5" Y="0.5"/>
                            </Border.RenderTransformOrigin>
                            <StackPanel Orientation="Horizontal"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Margin="15,0">
                                <TextBlock Text="{TemplateBinding Content}"
                                           Foreground="{TemplateBinding Foreground}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontWeight="{TemplateBinding FontWeight}"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                                <TextBlock x:Name="IconText"
                                           Text="{TemplateBinding Tag}"
                                           FontSize="18"
                                           VerticalAlignment="Center"
                                           Foreground="{StaticResource SecondaryColor}"/>
                            </StackPanel>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Window Control Button Style -->
        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="45"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E0E0E0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Close Button Style -->
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowControlButtonStyle}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E74C3C"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Window Border -->
    <Border Background="{StaticResource BackgroundColor}" CornerRadius="12">
        <Border.Effect>
            <DropShadowEffect Color="#B0BEC5" Opacity="0.4" BlurRadius="20" ShadowDepth="3"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="220"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Header Bar -->
            <Border Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2"
                    Background="{StaticResource HeaderColor}"
                    CornerRadius="10,10,0,0"
                    MouseLeftButtonDown="HeaderBar_MouseLeftButtonDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Window Control Buttons (Left Side) -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left" Margin="10,0,0,0">
                        <Button x:Name="CloseButton" Content="✕" Style="{StaticResource CloseButtonStyle}" Click="CloseButton_Click"/>
                        <Button x:Name="MaximizeButton" Content="🗖" Style="{StaticResource WindowControlButtonStyle}" Click="MaximizeButton_Click"/>
                        <Button x:Name="MinimizeButton" Content="🗕" Style="{StaticResource WindowControlButtonStyle}" Click="MinimizeButton_Click"/>
                    </StackPanel>

                    <!-- Application Title (Centered) -->
                    <Border Grid.Column="1"
                            Background="White"
                            CornerRadius="20"
                            Padding="15,8"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            BorderThickness="2"
                            BorderBrush="{StaticResource PrimaryColor}"
                            Opacity="0.95">
                        <Border.Effect>
                            <DropShadowEffect Color="#2196F3"
                                              Direction="270"
                                              ShadowDepth="0"
                                              BlurRadius="10"
                                              Opacity="0.3"/>
                        </Border.Effect>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📋"
                                       FontSize="22"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="نظام أرشفة الفواتير"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       FontFamily="Segoe UI"
                                       Foreground="{StaticResource PrimaryColor}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- Cloud Storage Button (Right Side) -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,15,0">
                        <Button x:Name="CloudStorageButton"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Background="#4285F4"
                               Foreground="White"
                               Width="140"
                               Height="35"
                               FontSize="12"
                               FontWeight="SemiBold"
                               ToolTip="إعدادات التخزين السحابي"
                               Click="CloudStorageButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CloudUpload" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="التخزين السحابي"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Sidebar -->
            <Border Grid.Row="1" Grid.Column="0"
                    Background="{StaticResource SidebarGradient}"
                    CornerRadius="0,0,0,12">
                <Border.Effect>
                    <DropShadowEffect Color="#90A4AE" Opacity="0.2" BlurRadius="8" ShadowDepth="2" Direction="0"/>
                </Border.Effect>
                <StackPanel Margin="0,20,0,0">
                    <!-- Main Tools Section Header -->
                    <Border Background="#E8F5E8"
                            CornerRadius="8"
                            Margin="15,0,15,10"
                            Padding="10,8">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <TextBlock Text="📋" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="الأدوات الرئيسية"
                                       FontSize="12"
                                       FontWeight="SemiBold"
                                       Foreground="#495057"/>
                        </StackPanel>
                    </Border>

                    <!-- Main Navigation Buttons -->
                    <Button x:Name="DashboardButton"
                            Content="لوحة التحكم"
                            Tag="📊"
                            Style="{StaticResource SelectedNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <Button x:Name="InvoicesButton"
                            Content="الفواتير"
                            Tag="📁"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <Button x:Name="PaymentsButton"
                            Content="المدفوعات"
                            Tag="💸"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <Button x:Name="SuppliersButton"
                            Content="الموردين"
                            Tag="🚚"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <Button x:Name="SearchButton"
                            Content="البحث"
                            Tag="🔍"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <Button x:Name="ReportsButton"
                            Content="التقارير"
                            Tag="📑"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <!-- Separator -->
                    <Border Height="1" Background="#E0E0E0" Margin="20,15,20,15"/>

                    <!-- Advanced Tools Section Header -->
                    <Border Background="#F0F8FF"
                            CornerRadius="8"
                            Margin="15,10,15,5"
                            Padding="10,8">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <TextBlock Text="🔧" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="الأدوات المتقدمة"
                                       FontSize="12"
                                       FontWeight="SemiBold"
                                       Foreground="#495057"/>
                        </StackPanel>
                    </Border>

                    <Button x:Name="SettingsButton"
                            Content="الإعدادات"
                            Tag="⚙️"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <Button x:Name="ImportExportButton"
                            Content="الاستيراد والتصدير"
                            Tag="📤"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <Button x:Name="BackupRestoreButton"
                            Content="النسخ الاحتياطي"
                            Tag="💾"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <Button x:Name="PerformanceButton"
                            Content="تحسين الأداء"
                            Tag="⚡"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Click="NavigationButton_Click"/>

                    <!-- Separator -->
                    <Border Height="1" Background="#E0E0E0" Margin="20,15,20,15"/>

                    <!-- Cloud Storage Section Header -->
                    <Border Background="#E3F2FD"
                            CornerRadius="8"
                            Margin="15,0,15,10"
                            Padding="10,8">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <TextBlock Text="☁️" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="الخدمات السحابية"
                                       FontSize="12"
                                       FontWeight="SemiBold"
                                       Foreground="#495057"/>
                        </StackPanel>
                    </Border>

                    <!-- Cloud Storage Button -->
                    <Button x:Name="SidebarCloudButton"
                            Content="التخزين السحابي"
                            Tag="☁️"
                            Style="{StaticResource ModernNavButtonStyle}"
                            Background="#4285F4"
                            Foreground="White"
                            FontWeight="SemiBold"
                            Click="CloudStorageButton_Click">
                        <Button.Effect>
                            <DropShadowEffect Color="#4285F4" Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
                        </Button.Effect>
                    </Button>
                </StackPanel>
            </Border>

            <!-- Main Content Area -->
            <Border Grid.Row="1" Grid.Column="1"
                    Background="White"
                    CornerRadius="0,0,10,0"
                    Margin="5,0,0,0">
                <ContentPresenter x:Name="MainContentPresenter" Margin="20"/>
            </Border>
        </Grid>
    </Border>
</Window>
