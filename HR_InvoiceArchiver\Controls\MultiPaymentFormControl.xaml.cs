using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.IO;
using Microsoft.Win32;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Data;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Media.Animation;
using System.Threading.Tasks;
using System.Windows.Media;
using System.Windows.Controls.Primitives;

namespace HR_InvoiceArchiver.Controls
{
    public partial class MultiPaymentFormControl : UserControl, INotifyPropertyChanged
    {
        private readonly InvoiceService _invoiceService;
        private readonly SupplierService _supplierService;
        private readonly PaymentService _paymentService;
        private MultiPaymentModel _multiPayment;
        private ObservableCollection<Supplier> _suppliers;
        private ObservableCollection<MultiPaymentInvoiceModel> _availableInvoices;

        public event EventHandler<MultiPaymentEventArgs>? FormClosed;

        public event PropertyChangedEventHandler? PropertyChanged;

        public MultiPaymentFormControl()
        {
            InitializeComponent();

            var context = new DatabaseContext();
            var invoiceRepository = new InvoiceRepository(context);
            var supplierRepository = new SupplierRepository(context);
            var paymentRepository = new PaymentRepository(context);

            _invoiceService = new InvoiceService(invoiceRepository, supplierRepository, paymentRepository);
            _supplierService = new SupplierService(supplierRepository, invoiceRepository, paymentRepository);
            _paymentService = new PaymentService(paymentRepository, invoiceRepository);
            _multiPayment = new MultiPaymentModel();
            _suppliers = new ObservableCollection<Supplier>();
            _availableInvoices = new ObservableCollection<MultiPaymentInvoiceModel>();

            DataContext = this;
            InitializeForm();
        }

        public MultiPaymentModel MultiPayment
        {
            get => _multiPayment;
            set
            {
                _multiPayment = value;
                OnPropertyChanged(nameof(MultiPayment));
            }
        }

        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set
            {
                _suppliers = value;
                OnPropertyChanged(nameof(Suppliers));
            }
        }

        public ObservableCollection<MultiPaymentInvoiceModel> AvailableInvoices
        {
            get => _availableInvoices;
            set
            {
                _availableInvoices = value;
                OnPropertyChanged(nameof(AvailableInvoices));
            }
        }

        private async void InitializeForm()
        {
            try
            {
                // تحميل الموردين
                var suppliers = await _supplierService.GetAllSuppliersAsync();
                Suppliers.Clear();
                foreach (var supplier in suppliers)
                {
                    Suppliers.Add(supplier);
                }
                SupplierComboBox.ItemsSource = Suppliers;

                // تعيين التاريخ الحالي
                PaymentDatePicker.SelectedDate = DateTime.Now;
                PaymentMethodComboBox.SelectedIndex = 0; // نقدي

                // ربط الأحداث
                MultiPayment.PropertyChanged += MultiPayment_PropertyChanged;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MultiPayment_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(MultiPaymentModel.SelectedInvoices))
            {
                UpdatePaymentSummary();
            }
        }

        private async void SupplierComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SupplierComboBox.SelectedItem is Supplier selectedSupplier)
            {
                try
                {
                    MultiPayment.SupplierId = selectedSupplier.Id;
                    MultiPayment.SupplierName = selectedSupplier.Name;

                    // تحميل الفواتير غير المدفوعة للمورد
                    var allInvoices = await _invoiceService.GetInvoicesBySupplierAsync(selectedSupplier.Id);
                    var invoices = allInvoices.Where(i => i.Status == InvoiceStatus.Unpaid || i.Status == InvoiceStatus.PartiallyPaid);
                    AvailableInvoices.Clear();

                    foreach (var invoice in invoices)
                    {
                        var multiPaymentInvoice = new MultiPaymentInvoiceModel
                        {
                            InvoiceId = invoice.Id,
                            InvoiceNumber = invoice.InvoiceNumber,
                            InvoiceDate = invoice.InvoiceDate,
                            InvoiceAmount = invoice.Amount,
                            RemainingAmount = invoice.RemainingAmount,
                            SupplierName = selectedSupplier.Name,
                            IsSelected = false
                        };
                        
                        multiPaymentInvoice.PropertyChanged += InvoiceItem_PropertyChanged;
                        AvailableInvoices.Add(multiPaymentInvoice);
                    }

                    InvoicesItemsControl.ItemsSource = AvailableInvoices;
                    UpdateStatusText();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل فواتير المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void InvoiceItem_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(MultiPaymentInvoiceModel.IsSelected))
            {
                UpdateSelectedInvoices();
            }
        }

        private void InvoiceCheckBox_Changed(object sender, RoutedEventArgs e)
        {
            UpdateSelectedInvoices();
        }

        private void UpdateSelectedInvoices()
        {
            var selectedInvoices = AvailableInvoices.Where(i => i.IsSelected).ToList();
            MultiPayment.SelectedInvoices = selectedInvoices;
            UpdatePaymentSummary();
            UpdateStatusText();
        }

        private void UpdatePaymentSummary()
        {
            var selectedInvoices = AvailableInvoices.Where(i => i.IsSelected).ToList();
            
            if (selectedInvoices.Any())
            {
                PaymentSummaryPanel.Visibility = Visibility.Visible;
                PaymentDetailsPanel.Visibility = Visibility.Visible;

                InvoicesCountText.Text = selectedInvoices.Count.ToString();
                TotalAmountText.Text = $"{selectedInvoices.Sum(i => i.RemainingAmount):N0} د.ع";
                
                MultiPayment.TotalInvoicesAmount = selectedInvoices.Sum(i => i.RemainingAmount);
                UpdateDiscountAndFinalAmount();
                
                SaveButton.IsEnabled = true;
            }
            else
            {
                PaymentSummaryPanel.Visibility = Visibility.Collapsed;
                PaymentDetailsPanel.Visibility = Visibility.Collapsed;
                SaveButton.IsEnabled = false;
            }
        }

        private void UpdateDiscountAndFinalAmount()
        {
            DiscountAmountText.Text = $"{MultiPayment.DiscountAmount:N0} د.ع";
            FinalAmountText.Text = $"{MultiPayment.FinalAmount:N0} د.ع";

            // عرض معلومات إضافية عن الخصم في StatusTextBlock
            if (MultiPayment.DiscountAmount > 0 && MultiPayment.TotalInvoicesAmount > 0)
            {
                decimal discountPercentage = (MultiPayment.DiscountAmount / MultiPayment.TotalInvoicesAmount) * 100;
                // يمكن عرض معلومات الخصم في StatusTextBlock أو إضافة عنصر UI منفصل لاحقاً
                System.Diagnostics.Debug.WriteLine($"Discount applied: {discountPercentage:F1}% of total invoices");
            }
        }

        private void UpdateStatusText()
        {
            var selectedCount = AvailableInvoices.Count(i => i.IsSelected);
            var totalCount = AvailableInvoices.Count;

            if (MultiPayment.SupplierId == 0)
            {
                StatusTextBlock.Text = "اختر المورد والفواتير للمتابعة";
            }
            else if (totalCount == 0)
            {
                StatusTextBlock.Text = "لا توجد فواتير غير مدفوعة لهذا المورد";
            }
            else if (selectedCount == 0)
            {
                StatusTextBlock.Text = $"اختر من {totalCount} فاتورة متاحة";
            }
            else
            {
                StatusTextBlock.Text = $"تم اختيار {selectedCount} من {totalCount} فاتورة";
            }
        }

        private void SelectAllButton_Click(object sender, RoutedEventArgs e)
        {
            foreach (var invoice in AvailableInvoices)
            {
                invoice.IsSelected = true;
            }
            UpdateSelectedInvoices();
        }

        private void ClearAllButton_Click(object sender, RoutedEventArgs e)
        {
            foreach (var invoice in AvailableInvoices)
            {
                invoice.IsSelected = false;
            }
            UpdateSelectedInvoices();
        }

        private void SmartDiscountButton_Click(object sender, RoutedEventArgs e)
        {
            MultiPayment.CalculateSmartDiscount();
            DiscountPercentageTextBox.Text = MultiPayment.DiscountPercentage.ToString("F1");
            UpdateDiscountAndFinalAmount();
        }

        private void DiscountPercentage_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(DiscountPercentageTextBox.Text, out decimal percentage))
            {
                // التحقق من صحة نسبة الخصم
                if (percentage < 0)
                {
                    percentage = 0;
                    DiscountPercentageTextBox.Text = "0";
                }
                else if (percentage > 100)
                {
                    percentage = 100;
                    DiscountPercentageTextBox.Text = "100";
                }

                MultiPayment.DiscountPercentage = percentage;
                UpdateDiscountAndFinalAmount();

                // عرض تحذير إذا كان الخصم مرتفعاً
                if (percentage > 50)
                {
                    ShowDiscountWarning($"⚠️ خصم مرتفع: {percentage:F1}%", "#FF9800");
                }
                else if (percentage > 0)
                {
                    ShowDiscountWarning($"✅ خصم مطبق: {percentage:F1}%", "#4CAF50");
                }
                else
                {
                    HideDiscountWarning();
                }
            }
        }

        private void SelectAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار مرفق الوصل",
                Filter = "جميع الملفات (*.*)|*.*|صور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|PDF (*.pdf)|*.pdf",
                FilterIndex = 2
            };

            if (openFileDialog.ShowDialog() == true)
            {
                AttachmentPathTextBox.Text = Path.GetFileName(openFileDialog.FileName);
                MultiPayment.AttachmentPath = openFileDialog.FileName;
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                SaveButton.IsEnabled = false;
                SaveButton.Content = "جاري الحفظ...";

                // تعيين البيانات من النموذج
                MultiPayment.ReceiptNumber = ReceiptNumberTextBox.Text;
                MultiPayment.PaymentDate = PaymentDatePicker.SelectedDate ?? DateTime.Now;
                MultiPayment.PaymentMethod = (PaymentMethodComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "نقدي";
                MultiPayment.Notes = NotesTextBox.Text;

                // حفظ الدفع المتعدد
                await _paymentService.SaveMultiPaymentAsync(MultiPayment);

                // إظهار رسالة النجاح مع إغلاق تلقائي
                ShowSuccessToastWithAutoClose("🎉 تم حفظ الوصل المتعدد بنجاح!");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الوصل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                SaveButton.IsEnabled = true;
                SaveButton.Content = "حفظ الوصل";
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الوصل", "تحقق من البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ReceiptNumberTextBox.Focus();
                return false;
            }

            if (MultiPayment.SelectedInvoices.Count == 0)
            {
                MessageBox.Show("يرجى اختيار فاتورة واحدة على الأقل", "تحقق من البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (MultiPayment.FinalAmount <= 0)
            {
                MessageBox.Show("المبلغ النهائي يجب أن يكون أكبر من صفر", "تحقق من البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            CloseWithAnimation(false);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            CloseWithAnimation(false);
        }

        private void CloseWithAnimation(bool success, MultiPaymentModel? multiPayment = null)
        {
            var storyboard = new Storyboard();

            // Fade out animation
            var fadeOut = new DoubleAnimation
            {
                From = 1.0,
                To = 0.0,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTarget(fadeOut, this);
            Storyboard.SetTargetProperty(fadeOut, new PropertyPath("Opacity"));

            // Scale down animation
            var scaleDownX = new DoubleAnimation
            {
                From = 1.0,
                To = 0.9,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTarget(scaleDownX, this);
            Storyboard.SetTargetProperty(scaleDownX, new PropertyPath("RenderTransform.ScaleX"));

            var scaleDownY = new DoubleAnimation
            {
                From = 1.0,
                To = 0.9,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTarget(scaleDownY, this);
            Storyboard.SetTargetProperty(scaleDownY, new PropertyPath("RenderTransform.ScaleY"));

            storyboard.Children.Add(fadeOut);
            storyboard.Children.Add(scaleDownX);
            storyboard.Children.Add(scaleDownY);

            storyboard.Completed += (s, e) =>
            {
                FormClosed?.Invoke(this, new MultiPaymentEventArgs { Success = success, MultiPayment = multiPayment });
            };

            storyboard.Begin();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void ShowSuccessToast(string message)
        {
            try
            {
                var mainWindow = Application.Current.MainWindow;
                if (mainWindow != null)
                {
                    // إنشاء Toast notification
                    var toast = new Border
                    {
                        Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)), // أخضر
                        CornerRadius = new CornerRadius(8),
                        Padding = new Thickness(20, 12, 20, 12),
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Top,
                        Margin = new Thickness(0, 80, 0, 0),
                        Opacity = 0,
                        Child = new TextBlock
                        {
                            Text = message,
                            Foreground = Brushes.White,
                            FontSize = 16,
                            FontWeight = FontWeights.Bold,
                            TextAlignment = TextAlignment.Center
                        }
                    };

                    // إضافة التأثير
                    toast.Effect = new System.Windows.Media.Effects.DropShadowEffect
                    {
                        Color = Colors.Black,
                        Direction = 270,
                        ShadowDepth = 4,
                        BlurRadius = 8,
                        Opacity = 0.3
                    };

                    // إضافة إلى النافذة الرئيسية
                    var mainGrid = mainWindow.Content as Grid;
                    if (mainGrid != null)
                    {
                        Panel.SetZIndex(toast, 9999);
                        mainGrid.Children.Add(toast);

                        // أنيميشن الظهور
                        var fadeIn = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
                        var slideDown = new DoubleAnimation(-30, 0, TimeSpan.FromMilliseconds(300));

                        var translateTransform = new TranslateTransform();
                        toast.RenderTransform = translateTransform;

                        toast.BeginAnimation(UIElement.OpacityProperty, fadeIn);
                        translateTransform.BeginAnimation(TranslateTransform.YProperty, slideDown);

                        // إزالة بعد 3 ثوان
                        var timer = new System.Windows.Threading.DispatcherTimer
                        {
                            Interval = TimeSpan.FromSeconds(3)
                        };
                        timer.Tick += (s, e) =>
                        {
                            timer.Stop();
                            var fadeOut = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(300));
                            fadeOut.Completed += (sender, args) => mainGrid.Children.Remove(toast);
                            toast.BeginAnimation(UIElement.OpacityProperty, fadeOut);
                        };
                        timer.Start();
                    }
                }
            }
            catch
            {
                // في حالة فشل Toast، استخدم MessageBox كبديل
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ShowSuccessToastWithAutoClose(string message)
        {
            try
            {
                var mainWindow = Application.Current.MainWindow;
                if (mainWindow != null)
                {
                    // إنشاء Toast notification
                    var toast = new Border
                    {
                        Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)), // أخضر
                        CornerRadius = new CornerRadius(8),
                        Padding = new Thickness(20, 12, 20, 12),
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Top,
                        Margin = new Thickness(0, 80, 0, 0),
                        Opacity = 0,
                        Child = new TextBlock
                        {
                            Text = message,
                            Foreground = Brushes.White,
                            FontSize = 16,
                            FontWeight = FontWeights.Bold,
                            TextAlignment = TextAlignment.Center
                        }
                    };

                    // إضافة التأثير
                    toast.Effect = new System.Windows.Media.Effects.DropShadowEffect
                    {
                        Color = Colors.Black,
                        Direction = 270,
                        ShadowDepth = 4,
                        BlurRadius = 8,
                        Opacity = 0.3
                    };

                    // إضافة إلى النافذة الرئيسية
                    var mainGrid = mainWindow.Content as Grid;
                    if (mainGrid != null)
                    {
                        Panel.SetZIndex(toast, 9999);
                        mainGrid.Children.Add(toast);

                        // أنيميشن الظهور
                        var fadeIn = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
                        var slideDown = new DoubleAnimation(-30, 0, TimeSpan.FromMilliseconds(300));

                        var translateTransform = new TranslateTransform();
                        toast.RenderTransform = translateTransform;

                        toast.BeginAnimation(UIElement.OpacityProperty, fadeIn);
                        translateTransform.BeginAnimation(TranslateTransform.YProperty, slideDown);

                        // إزالة بعد 4 ثوان مع إغلاق النافذة
                        var timer = new System.Windows.Threading.DispatcherTimer
                        {
                            Interval = TimeSpan.FromSeconds(4)
                        };
                        timer.Tick += (s, e) =>
                        {
                            timer.Stop();
                            var fadeOut = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(300));
                            fadeOut.Completed += (sender, args) =>
                            {
                                mainGrid.Children.Remove(toast);
                                // إغلاق النافذة بعد اختفاء رسالة النجاح
                                CloseWithAnimation(true, MultiPayment);
                            };
                            toast.BeginAnimation(UIElement.OpacityProperty, fadeOut);
                        };
                        timer.Start();
                    }
                }
            }
            catch
            {
                // في حالة فشل Toast، استخدم MessageBox كبديل ثم أغلق النافذة
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                CloseWithAnimation(true, MultiPayment);
            }
        }

        private void ShowDiscountWarning(string message, string color)
        {
            // يمكن إضافة عنصر UI لعرض التحذيرات إذا لزم الأمر
            // أو استخدام StatusTextBlock الموجود
            StatusTextBlock.Text = message;
            StatusTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(color));
        }

        private void HideDiscountWarning()
        {
            // إعادة StatusTextBlock إلى حالته الطبيعية
            UpdateStatusText();
        }
    }

    public class MultiPaymentEventArgs : EventArgs
    {
        public bool Success { get; set; }
        public MultiPaymentModel? MultiPayment { get; set; }
    }
}
