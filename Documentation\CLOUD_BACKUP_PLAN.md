# خطة نظام حفظ المرفقات السحابي المجاني

## 🔍 **تحليل المشكلة الحالية**

### المشاكل الموجودة:
1. **حفظ محلي فقط**: المرفقات تُحفظ في مجلد محلي فقط
2. **خطر فقدان البيانات**: في حالة تلف الجهاز أو القرص الصلب
3. **عدم المزامنة**: لا يمكن الوصول للمرفقات من أجهزة أخرى
4. **عدم وجود نسخ احتياطية**: لا توجد آلية للنسخ الاحتياطي

### المسار الحالي:
```
C:\Users\<USER>\AppData\Local\HR_InvoiceArchiver\Attachments\
├── Invoices\
└── Payments\
```

---

## 🎯 **الحلول المجانية المقترحة**

### 1. **Google Drive API** (الأفضل) ⭐
- **المساحة المجانية**: 15 جيجابايت
- **المميزات**: API قوي، مزامنة تلقائية، أمان عالي
- **التكلفة**: مجاني تماماً

### 2. **OneDrive API**
- **المساحة المجانية**: 5 جيجابايت
- **المميزات**: تكامل مع Windows، سهولة الاستخدام
- **التكلفة**: مجاني

### 3. **Dropbox API**
- **المساحة المجانية**: 2 جيجابايت
- **المميزات**: سرعة عالية، واجهة بسيطة
- **التكلفة**: مجاني

### 4. **Firebase Storage** (Google)
- **المساحة المجانية**: 5 جيجابايت
- **المميزات**: سرعة عالية، أمان ممتاز
- **التكلفة**: مجاني

---

## 🚀 **الخطة المقترحة: Google Drive Integration**

### المرحلة الأولى: الإعداد الأساسي

#### 1. **إضافة Google Drive API**
```xml
<!-- في ملف .csproj -->
<PackageReference Include="Google.Apis.Drive.v3" Version="1.68.0.3413" />
<PackageReference Include="Google.Apis.Auth" Version="1.68.0" />
```

#### 2. **إنشاء خدمة السحابة**
```csharp
public interface ICloudStorageService
{
    Task<string> UploadFileAsync(string filePath, string fileName);
    Task<bool> DownloadFileAsync(string cloudFileId, string localPath);
    Task<bool> DeleteFileAsync(string cloudFileId);
    Task<bool> FileExistsAsync(string cloudFileId);
}
```

### المرحلة الثانية: التطبيق

#### 1. **هيكل المجلدات في Google Drive**
```
HR_InvoiceArchiver/
├── Invoices/
│   ├── 2024/
│   │   ├── 01/
│   │   └── 02/
└── Payments/
    ├── 2024/
    │   ├── 01/
    │   └── 02/
```

#### 2. **نظام التسمية**
```
INV_[InvoiceNumber]_[Timestamp]_[UniqueId].[ext]
PAY_[ReceiptNumber]_[Timestamp]_[UniqueId].[ext]
```

---

## 💾 **استراتيجية النسخ الاحتياطي**

### النظام المختلط (Hybrid):
1. **حفظ محلي**: للوصول السريع
2. **رفع سحابي**: للنسخ الاحتياطي
3. **مزامنة تلقائية**: كل 30 دقيقة

### آلية العمل:
```
1. المستخدم يرفع مرفق
2. حفظ في المجلد المحلي
3. رفع تلقائي للسحابة
4. حفظ معرف السحابة في قاعدة البيانات
5. التحقق الدوري من المزامنة
```

---

## 🛠️ **التطبيق العملي**

### 1. **إنشاء خدمة Google Drive**

```csharp
public class GoogleDriveService : ICloudStorageService
{
    private DriveService _driveService;
    private string _applicationName = "HR Invoice Archiver";
    private string _folderId; // معرف مجلد التطبيق

    public async Task<string> UploadFileAsync(string filePath, string fileName)
    {
        var fileMetadata = new Google.Apis.Drive.v3.Data.File()
        {
            Name = fileName,
            Parents = new List<string> { _folderId }
        };

        using var stream = new FileStream(filePath, FileMode.Open);
        var request = _driveService.Files.Create(fileMetadata, stream, GetMimeType(filePath));
        
        var result = await request.UploadAsync();
        return request.ResponseBody?.Id ?? string.Empty;
    }
}
```

### 2. **تحديث FileHelper**

```csharp
public static async Task<(string localPath, string cloudId)> SaveAttachmentWithBackupAsync(
    string sourceFilePath, string targetDirectory, string prefix = "")
{
    // حفظ محلي
    var localFileName = await SaveAttachmentAsync(sourceFilePath, targetDirectory, prefix);
    
    // رفع سحابي
    var cloudService = new GoogleDriveService();
    var cloudId = await cloudService.UploadFileAsync(
        Path.Combine(targetDirectory, localFileName), 
        localFileName);
    
    return (localFileName, cloudId);
}
```

### 3. **تحديث قاعدة البيانات**

```sql
-- إضافة عمود للمعرف السحابي
ALTER TABLE Payments ADD COLUMN CloudFileId TEXT;
ALTER TABLE Invoices ADD COLUMN CloudFileId TEXT;

-- إضافة عمود لحالة المزامنة
ALTER TABLE Payments ADD COLUMN SyncStatus TEXT DEFAULT 'Pending';
ALTER TABLE Invoices ADD COLUMN SyncStatus TEXT DEFAULT 'Pending';
```

---

## 🔄 **نظام المزامنة التلقائية**

### خدمة المزامنة:
```csharp
public class SyncService
{
    private Timer _syncTimer;
    
    public void StartAutoSync()
    {
        _syncTimer = new Timer(SyncPendingFiles, null, 
            TimeSpan.Zero, TimeSpan.FromMinutes(30));
    }
    
    private async void SyncPendingFiles(object state)
    {
        // البحث عن الملفات غير المتزامنة
        var pendingFiles = await GetPendingFiles();
        
        foreach (var file in pendingFiles)
        {
            await SyncFile(file);
        }
    }
}
```

---

## 📱 **واجهة المستخدم للسحابة**

### إضافة قسم إعدادات السحابة:
```xml
<GroupBox Header="إعدادات النسخ الاحتياطي السحابي" Margin="0,10,0,0">
    <StackPanel>
        <CheckBox x:Name="EnableCloudBackup" Content="تفعيل النسخ الاحتياطي السحابي"/>
        
        <ComboBox x:Name="CloudProvider" Margin="0,10,0,0">
            <ComboBoxItem Content="Google Drive" IsSelected="True"/>
            <ComboBoxItem Content="OneDrive"/>
            <ComboBoxItem Content="Dropbox"/>
        </ComboBox>
        
        <Button Content="ربط الحساب" Margin="0,10,0,0"/>
        <Button Content="مزامنة الآن" Margin="0,5,0,0"/>
        
        <TextBlock x:Name="SyncStatus" Text="آخر مزامنة: لم تتم بعد" 
                   Margin="0,10,0,0" FontSize="12"/>
    </StackPanel>
</GroupBox>
```

---

## 🔐 **الأمان والخصوصية**

### 1. **تشفير الملفات**
```csharp
public static byte[] EncryptFile(byte[] fileData, string password)
{
    using var aes = Aes.Create();
    aes.Key = Encoding.UTF8.GetBytes(password.PadRight(32).Substring(0, 32));
    aes.IV = new byte[16]; // يمكن تحسينه
    
    using var encryptor = aes.CreateEncryptor();
    return encryptor.TransformFinalBlock(fileData, 0, fileData.Length);
}
```

### 2. **إعدادات الخصوصية**
- تشفير الملفات قبل الرفع
- استخدام أسماء ملفات مشفرة
- حذف تلقائي للملفات القديمة

---

## 📊 **مراقبة الاستخدام**

### لوحة معلومات السحابة:
```xml
<StackPanel>
    <TextBlock Text="إحصائيات التخزين السحابي" FontWeight="Bold"/>
    
    <ProgressBar x:Name="StorageUsage" Maximum="15" Value="2.5" 
                 Height="20" Margin="0,10,0,0"/>
    
    <TextBlock x:Name="StorageText" Text="2.5 GB / 15 GB مستخدم"
               HorizontalAlignment="Center"/>
    
    <TextBlock x:Name="FilesCount" Text="عدد الملفات: 1,234"
               Margin="0,10,0,0"/>
    
    <TextBlock x:Name="LastSync" Text="آخر مزامنة: منذ 5 دقائق"
               Margin="0,5,0,0"/>
</StackPanel>
```

---

## 🎯 **خطة التطبيق المرحلية**

### المرحلة 1 (أسبوع واحد):
- ✅ إعداد Google Drive API
- ✅ إنشاء خدمة الرفع الأساسية
- ✅ تحديث قاعدة البيانات

### المرحلة 2 (أسبوع واحد):
- ✅ تطبيق النسخ الاحتياطي التلقائي
- ✅ إضافة واجهة الإعدادات
- ✅ نظام المزامنة

### المرحلة 3 (أسبوع واحد):
- ✅ إضافة التشفير
- ✅ نظام المراقبة
- ✅ معالجة الأخطاء

---

## 💰 **التكلفة والفوائد**

### التكلفة:
- **مجاني تماماً** حتى 15 جيجابايت
- **بعد 15 جيجابايت**: $1.99/شهر لـ 100 جيجابايت

### الفوائد:
- ✅ **أمان البيانات**: حماية من فقدان الملفات
- ✅ **الوصول من أي مكان**: مزامنة عبر الأجهزة
- ✅ **نسخ احتياطية تلقائية**: بدون تدخل المستخدم
- ✅ **توفير مساحة محلية**: إمكانية حذف الملفات القديمة

---

## 🚀 **البدء السريع**

### الخطوات الأولى:
1. **إنشاء مشروع Google Cloud**
2. **تفعيل Drive API**
3. **إنشاء credentials**
4. **تطبيق الكود الأساسي**

هل تريد مني البدء في تطبيق هذه الخطة؟ يمكنني البدء بالمرحلة الأولى وإنشاء الخدمات الأساسية.
