using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using MaterialDesignThemes.Wpf;

namespace HR_InvoiceArchiver.Controls.Dashboard
{
    public partial class RecentActivitiesControl : UserControl
    {
        private readonly IInvoiceService? _invoiceService;
        private readonly IDashboardService? _dashboardService;
        private readonly INavigationService? _navigationService;

        public RecentActivitiesControl()
        {
            InitializeComponent();
        }

        public RecentActivitiesControl(
            IInvoiceService invoiceService, 
            IDashboardService dashboardService,
            INavigationService navigationService) : this()
        {
            _invoiceService = invoiceService;
            _dashboardService = dashboardService;
            _navigationService = navigationService;
        }

        public async Task LoadRecentInvoices(int count = 5)
        {
            try
            {
                if (_invoiceService == null) return;

                var invoices = await _invoiceService.GetAllInvoicesBasicAsync();
                var recentInvoices = invoices
                    .OrderByDescending(i => i.CreatedDate)
                    .Take(count)
                    .Select(i => new RecentInvoiceViewModel
                    {
                        InvoiceNumber = i.InvoiceNumber,
                        SupplierName = i.Supplier?.Name ?? "غير محدد",
                        Amount = i.Amount,
                        InvoiceDate = i.InvoiceDate,
                        StatusText = GetStatusText(i.Status),
                        StatusColor = GetStatusColor(i.Status)
                    })
                    .ToList();

                RecentInvoicesItemsControl.ItemsSource = recentInvoices;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading recent invoices: {ex.Message}");
            }
        }

        public async Task LoadAlerts()
        {
            try
            {
                if (_dashboardService == null) return;

                var alerts = await _dashboardService.GetDashboardAlertsAsync();
                var alertViewModels = alerts.Take(5).Select(a => new AlertViewModel
                {
                    Title = a.Title,
                    Message = a.Message,
                    Timestamp = a.Timestamp,
                    IconKind = GetAlertIcon(a.Icon),
                    IconColor = GetAlertColor(ConvertAlertType(a.Type))
                }).ToList();

                AlertsItemsControl.ItemsSource = alertViewModels;
                NoAlertsPanel.Visibility = alertViewModels.Any() ? Visibility.Collapsed : Visibility.Visible;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading alerts: {ex.Message}");
                NoAlertsPanel.Visibility = Visibility.Visible;
            }
        }

        private string GetStatusText(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Unpaid => "غير مسددة",
                InvoiceStatus.PartiallyPaid => "مسددة جزئياً",
                InvoiceStatus.Paid => "مسددة",
                InvoiceStatus.PaidWithDiscount => "مسددة بخصم",
                _ => "غير محدد"
            };
        }

        private Brush GetStatusColor(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Unpaid => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                InvoiceStatus.PartiallyPaid => new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                InvoiceStatus.Paid => new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                InvoiceStatus.PaidWithDiscount => new SolidColorBrush(Color.FromRgb(139, 195, 74)),
                _ => new SolidColorBrush(Color.FromRgb(158, 158, 158))
            };
        }

        private PackIconKind GetAlertIcon(string iconName)
        {
            return iconName switch
            {
                "CheckCircle" => PackIconKind.CheckCircle,
                "AlertCircle" => PackIconKind.AlertCircle,
                "ClockAlert" => PackIconKind.ClockAlert,
                "Information" => PackIconKind.Information,
                _ => PackIconKind.Information
            };
        }

        private AlertType ConvertAlertType(Services.AlertType serviceType)
        {
            return serviceType switch
            {
                Services.AlertType.Success => AlertType.Success,
                Services.AlertType.Warning => AlertType.Warning,
                Services.AlertType.Error => AlertType.Error,
                Services.AlertType.Info => AlertType.Info,
                _ => AlertType.Info
            };
        }

        private Brush GetAlertColor(AlertType type)
        {
            return type switch
            {
                AlertType.Success => new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                AlertType.Warning => new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                AlertType.Error => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                AlertType.Info => new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                _ => new SolidColorBrush(Color.FromRgb(158, 158, 158))
            };
        }

        private void ViewAllInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _navigationService?.NavigateTo(typeof(Pages.InvoicesPage), "");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Navigation error: {ex.Message}");
            }
        }

        private async void ClearAlertsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_dashboardService != null)
                {
                    await _dashboardService.ClearAllAlertsAsync();
                    await LoadAlerts();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing alerts: {ex.Message}");
            }
        }
    }

    // ViewModels for simplified data binding
    public class RecentInvoiceViewModel
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public string SupplierName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string StatusText { get; set; } = string.Empty;
        public Brush StatusColor { get; set; } = Brushes.Gray;
    }

    public class AlertViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public PackIconKind IconKind { get; set; }
        public Brush IconColor { get; set; } = Brushes.Gray;
    }

    // Simplified enums
    public enum AlertType
    {
        Info,
        Success,
        Warning,
        Error
    }
}
