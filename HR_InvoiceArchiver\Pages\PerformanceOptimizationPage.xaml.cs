using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class PerformanceOptimizationPage : UserControl, INavigationAware
    {
        private readonly IPerformanceOptimizationService _performanceService;
        private readonly IToastService _toastService;
        private readonly ILoggingService _loggingService;
        private bool _isLoading = false;

        public PerformanceOptimizationPage()
        {
            InitializeComponent();
            
            // الحصول على الخدمات من DI Container
            var serviceProvider = App.ServiceProvider;
            _performanceService = serviceProvider.GetRequiredService<IPerformanceOptimizationService>();
            _toastService = serviceProvider.GetRequiredService<IToastService>();
            _loggingService = serviceProvider.GetRequiredService<ILoggingService>();
        }

        public async void OnNavigatedTo(object parameter)
        {
            await LoadPerformanceDataAsync();
        }

        public void OnNavigatedFrom()
        {
            // Cleanup when leaving this page
        }

        private async Task LoadPerformanceDataAsync()
        {
            if (_isLoading) return;

            try
            {
                SetLoadingState(true, "جاري تحميل بيانات الأداء...");

                // تحليل أداء قاعدة البيانات
                var dbReport = await _performanceService.AnalyzeDatabasePerformanceAsync();
                UpdateDatabasePerformanceUI(dbReport);

                // تحليل استخدام الذاكرة
                var memoryInfo = await _performanceService.GetMemoryUsageInfoAsync();
                UpdateMemoryUsageUI(memoryInfo);

                // تحليل الاستعلامات البطيئة
                var slowQueries = await _performanceService.AnalyzeSlowQueriesAsync();
                SlowQueriesDataGrid.ItemsSource = slowQueries;

                await _loggingService.LogInformationAsync("تم تحميل صفحة تحسين الأداء");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في تحميل بيانات الأداء");
                await _loggingService.LogErrorAsync("فشل في تحميل بيانات الأداء", ex);
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private void UpdateDatabasePerformanceUI(DatabasePerformanceReport report)
        {
            // تحديث المؤشرات الأساسية
            DatabaseSizeTextBlock.Text = FormatFileSize(report.DatabaseSizeBytes);
            QueryTimeTextBlock.Text = $"{report.AverageQueryTimeMs:F1} مللي ثانية";
            IndexesTextBlock.Text = $"{report.TotalIndexes} ({report.MissingIndexes} مفقود)";
            
            // تحديث درجة الأداء
            UpdatePerformanceScore(report.OverallScore);
            
            // تحديث التوصيات
            RecommendationsItemsControl.ItemsSource = report.Recommendations;
        }

        private void UpdatePerformanceScore(PerformanceScore score)
        {
            ScoreTextBlock.Text = GetScoreText(score);
            
            var (color, icon) = GetScoreAppearance(score);
            ScoreIcon.Kind = icon;
            ScoreIcon.Foreground = new SolidColorBrush(color);
            ScoreTextBlock.Foreground = new SolidColorBrush(color);
        }

        private void UpdateMemoryUsageUI(MemoryUsageInfo memoryInfo)
        {
            MemoryUsageGrid.Children.Clear();
            MemoryUsageGrid.RowDefinitions.Clear();
            MemoryUsageGrid.ColumnDefinitions.Clear();

            // إعداد الأعمدة
            MemoryUsageGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            MemoryUsageGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            var memoryItems = new[]
            {
                ("إجمالي الذاكرة", FormatFileSize(memoryInfo.TotalMemoryBytes)),
                ("الذاكرة المستخدمة", FormatFileSize(memoryInfo.UsedMemoryBytes)),
                ("الذاكرة المتاحة", FormatFileSize(memoryInfo.AvailableMemoryBytes)),
                ("نسبة الاستخدام", $"{memoryInfo.UsagePercentage:F1}%"),
                ("ذاكرة التخزين المؤقت", FormatFileSize(memoryInfo.CacheMemoryBytes)),
                ("ذاكرة GC", FormatFileSize(memoryInfo.GCMemoryBytes)),
                ("عمليات GC", memoryInfo.GCCollections.ToString())
            };

            for (int i = 0; i < memoryItems.Length; i++)
            {
                MemoryUsageGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                var labelTextBlock = new TextBlock
                {
                    Text = memoryItems[i].Item1 + ":",
                    FontWeight = FontWeights.Medium,
                    Margin = new Thickness(0, 8, 16, 8),
                    VerticalAlignment = VerticalAlignment.Center
                };
                Grid.SetRow(labelTextBlock, i);
                Grid.SetColumn(labelTextBlock, 0);

                var valueTextBlock = new TextBlock
                {
                    Text = memoryItems[i].Item2,
                    Margin = new Thickness(0, 8, 0, 8),
                    VerticalAlignment = VerticalAlignment.Center
                };
                Grid.SetRow(valueTextBlock, i);
                Grid.SetColumn(valueTextBlock, 1);

                MemoryUsageGrid.Children.Add(labelTextBlock);
                MemoryUsageGrid.Children.Add(valueTextBlock);
            }
        }

        private void SetLoadingState(bool isLoading, string message = "جاري المعالجة...")
        {
            _isLoading = isLoading;
            LoadingGrid.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            LoadingTextBlock.Text = message;
            
            // تعطيل/تفعيل الأزرار
            var buttons = new[] 
            { 
                RefreshButton, OptimizeAllButton, AnalyzeButton, CreateIndexesButton,
                UpdateStatsButton, CleanupButton, CompressButton, OptimizeConnectionsButton,
                ScheduleOptimizationButton, RefreshMemoryButton, ClearCacheButton, OptimizeCacheButton
            };

            foreach (var button in buttons)
            {
                button.IsEnabled = !isLoading;
            }
        }

        #region Event Handlers

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadPerformanceDataAsync();
        }

        private async void OptimizeAllButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isLoading) return;

            var result = MessageBox.Show(
                "هل تريد تشغيل التحسين الشامل؟\nسيتم تنفيذ جميع إجراءات التحسين المتاحة.",
                "تأكيد التحسين الشامل",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    SetLoadingState(true, "جاري تنفيذ التحسين الشامل...");

                    var tasks = new List<Task<bool>>
                    {
                        _performanceService.CreateOptimalIndexesAsync(),
                        _performanceService.UpdateDatabaseStatisticsAsync(),
                        _performanceService.CompressDatabaseAsync(),
                        _performanceService.OptimizeConnectionSettingsAsync()
                    };

                    var results = await Task.WhenAll(tasks);
                    var successCount = results.Count(r => r);

                    _toastService.ShowSuccess("تم التحسين", $"تم تنفيذ {successCount} من {results.Length} إجراء تحسين بنجاح");
                    
                    // تحديث البيانات
                    await LoadPerformanceDataAsync();
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ", "فشل في تنفيذ التحسين الشامل");
                    await _loggingService.LogErrorAsync("فشل في التحسين الشامل", ex);
                }
                finally
                {
                    SetLoadingState(false);
                }
            }
        }

        private async void AnalyzeButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadPerformanceDataAsync();
        }

        private async void CreateIndexesButton_Click(object sender, RoutedEventArgs e)
        {
            await ExecuteOptimizationAction(
                () => _performanceService.CreateOptimalIndexesAsync(),
                "إنشاء الفهارس",
                "جاري إنشاء الفهارس...",
                "تم إنشاء الفهارس بنجاح"
            );
        }

        private async void UpdateStatsButton_Click(object sender, RoutedEventArgs e)
        {
            await ExecuteOptimizationAction(
                () => _performanceService.UpdateDatabaseStatisticsAsync(),
                "تحديث الإحصائيات",
                "جاري تحديث إحصائيات قاعدة البيانات...",
                "تم تحديث الإحصائيات بنجاح"
            );
        }

        private async void CleanupButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isLoading) return;

            var result = MessageBox.Show(
                "هل تريد تنظيف قاعدة البيانات؟\nسيتم حذف البيانات القديمة والغير ضرورية.",
                "تأكيد التنظيف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    SetLoadingState(true, "جاري تنظيف قاعدة البيانات...");

                    var cleanupResult = await _performanceService.CleanupDatabaseAsync();
                    
                    if (cleanupResult.Success)
                    {
                        _toastService.ShowSuccess("تم التنظيف", 
                            $"تم حذف {cleanupResult.DeletedRecords} سجل وتحرير {FormatFileSize(cleanupResult.FreedSpaceBytes)}");
                        await LoadPerformanceDataAsync();
                    }
                    else
                    {
                        _toastService.ShowError("فشل التنظيف", cleanupResult.ErrorMessage ?? "حدث خطأ غير معروف");
                    }
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ", "فشل في تنظيف قاعدة البيانات");
                    await _loggingService.LogErrorAsync("فشل في تنظيف قاعدة البيانات", ex);
                }
                finally
                {
                    SetLoadingState(false);
                }
            }
        }

        private async void CompressButton_Click(object sender, RoutedEventArgs e)
        {
            await ExecuteOptimizationAction(
                () => _performanceService.CompressDatabaseAsync(),
                "ضغط قاعدة البيانات",
                "جاري ضغط قاعدة البيانات...",
                "تم ضغط قاعدة البيانات بنجاح"
            );
        }

        private async void OptimizeConnectionsButton_Click(object sender, RoutedEventArgs e)
        {
            await ExecuteOptimizationAction(
                () => _performanceService.OptimizeConnectionSettingsAsync(),
                "تحسين الاتصالات",
                "جاري تحسين إعدادات الاتصال...",
                "تم تحسين إعدادات الاتصال بنجاح"
            );
        }

        private async void ScheduleOptimizationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await _performanceService.ScheduleAutomaticOptimizationAsync();
                _toastService.ShowSuccess("تم التفعيل", "تم تفعيل جدولة التحسين التلقائي");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في تفعيل جدولة التحسين التلقائي");
                await _loggingService.LogErrorAsync("فشل في جدولة التحسين التلقائي", ex);
            }
        }

        private async void RefreshMemoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SetLoadingState(true, "جاري تحديث معلومات الذاكرة...");
                var memoryInfo = await _performanceService.GetMemoryUsageInfoAsync();
                UpdateMemoryUsageUI(memoryInfo);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في تحديث معلومات الذاكرة");
                await _loggingService.LogErrorAsync("فشل في تحديث معلومات الذاكرة", ex);
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async void ClearCacheButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SetLoadingState(true, "جاري مسح ذاكرة التخزين المؤقت...");
                await _performanceService.ClearCacheAsync();
                _toastService.ShowSuccess("تم المسح", "تم مسح ذاكرة التخزين المؤقت بنجاح");
                
                // تحديث معلومات الذاكرة
                var memoryInfo = await _performanceService.GetMemoryUsageInfoAsync();
                UpdateMemoryUsageUI(memoryInfo);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في مسح ذاكرة التخزين المؤقت");
                await _loggingService.LogErrorAsync("فشل في مسح ذاكرة التخزين المؤقت", ex);
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async void OptimizeCacheButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SetLoadingState(true, "جاري تحسين ذاكرة التخزين المؤقت...");
                await _performanceService.OptimizeCacheSizeAsync();
                _toastService.ShowSuccess("تم التحسين", "تم تحسين ذاكرة التخزين المؤقت بنجاح");
                
                // تحديث معلومات الذاكرة
                var memoryInfo = await _performanceService.GetMemoryUsageInfoAsync();
                UpdateMemoryUsageUI(memoryInfo);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في تحسين ذاكرة التخزين المؤقت");
                await _loggingService.LogErrorAsync("فشل في تحسين ذاكرة التخزين المؤقت", ex);
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        #endregion

        #region Helper Methods

        private async Task ExecuteOptimizationAction(
            Func<Task<bool>> action,
            string actionName,
            string loadingMessage,
            string successMessage)
        {
            if (_isLoading) return;

            try
            {
                SetLoadingState(true, loadingMessage);
                var success = await action();

                if (success)
                {
                    _toastService.ShowSuccess("نجح التحسين", successMessage);
                    await LoadPerformanceDataAsync();
                }
                else
                {
                    _toastService.ShowError("فشل التحسين", $"فشل في {actionName}");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"حدث خطأ أثناء {actionName}");
                await _loggingService.LogErrorAsync($"فشل في {actionName}", ex);
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";

            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:0.##} {sizes[order]}";
        }

        private string GetScoreText(PerformanceScore score)
        {
            return score switch
            {
                PerformanceScore.Excellent => "ممتاز",
                PerformanceScore.Good => "جيد",
                PerformanceScore.Average => "متوسط",
                PerformanceScore.Poor => "ضعيف",
                PerformanceScore.Critical => "حرج",
                _ => "غير معروف"
            };
        }

        private (Color color, MaterialDesignThemes.Wpf.PackIconKind icon) GetScoreAppearance(PerformanceScore score)
        {
            return score switch
            {
                PerformanceScore.Excellent => (Colors.Green, MaterialDesignThemes.Wpf.PackIconKind.Star),
                PerformanceScore.Good => (Colors.LightGreen, MaterialDesignThemes.Wpf.PackIconKind.ThumbUp),
                PerformanceScore.Average => (Colors.Orange, MaterialDesignThemes.Wpf.PackIconKind.Minus),
                PerformanceScore.Poor => (Colors.Red, MaterialDesignThemes.Wpf.PackIconKind.ThumbDown),
                PerformanceScore.Critical => (Colors.DarkRed, MaterialDesignThemes.Wpf.PackIconKind.Alert),
                _ => (Colors.Gray, MaterialDesignThemes.Wpf.PackIconKind.Help)
            };
        }

        #endregion
    }
}
