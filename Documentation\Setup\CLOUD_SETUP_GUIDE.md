# 🌟 دليل إعداد النسخ الاحتياطي السحابي

## 📋 **ما هو مطلوب منك:**

### ✅ **الأمور البسيطة (ستقوم بها مرة واحدة فقط):**
1. **إنشاء مشروع Google Cloud** (مجاني 100%)
2. **تحميل ملف credentials.json** (ملف صغير)
3. **وضع الملف في مجلد البرنامج**

### ❌ **ما لا تحتاجه:**
- ❌ تسجيل دخول خارجي
- ❌ برامج إضافية
- ❌ معرفة تقنية متقدمة
- ❌ دفع أي رسوم

---

## 🚀 **خطوات الإعداد (10 دقائق فقط):**

### الخطوة 1: إنشاء مشروع Google Cloud

1. **اذهب إلى**: [Google Cloud Console](https://console.cloud.google.com/)
2. **سجل دخول** بحساب Google الخاص بك
3. **انقر على "إنشاء مشروع"** أو "Create Project"
4. **اكتب اسم المشروع**: `HR Invoice Archiver`
5. **انقر "إنشاء"** أو "Create"

### الخطوة 2: تفعيل Google Drive API

1. **في القائمة الجانبية** → انقر "APIs & Services" → "Library"
2. **ابحث عن**: `Google Drive API`
3. **انقر على النتيجة الأولى**
4. **انقر "تفعيل"** أو "Enable"

### الخطوة 3: إنشاء Credentials

1. **اذهب إلى**: "APIs & Services" → "Credentials"
2. **انقر "+ CREATE CREDENTIALS"**
3. **اختر "OAuth client ID"**
4. **إذا طُلب منك إعداد OAuth consent screen:**
   - انقر "CONFIGURE CONSENT SCREEN"
   - اختر "External"
   - املأ المعلومات الأساسية:
     - **App name**: `HR Invoice Archiver`
     - **User support email**: بريدك الإلكتروني
     - **Developer contact**: بريدك الإلكتروني
   - انقر "Save and Continue" حتى النهاية

5. **العودة إلى Credentials:**
   - **Application type**: اختر "Desktop application"
   - **Name**: `HR Invoice Archiver Desktop`
   - انقر "CREATE"

### الخطوة 4: تحميل ملف Credentials

1. **بعد إنشاء Credentials** ستظهر نافذة
2. **انقر "DOWNLOAD JSON"**
3. **احفظ الملف باسم**: `credentials.json`

### الخطوة 5: وضع الملف في البرنامج

1. **انسخ ملف `credentials.json`**
2. **ضعه في مجلد البرنامج** بجانب ملف `HR_InvoiceArchiver.exe`

```
📁 مجلد البرنامج/
├── 📄 HR_InvoiceArchiver.exe
├── 📄 credentials.json  ← هنا
└── 📁 ملفات أخرى...
```

---

## 🎯 **كيف يعمل النظام:**

### 🔄 **النظام المختلط (Hybrid):**
```
1. تضيف مرفق جديد
   ↓
2. يُحفظ محلياً (للسرعة)
   ↓
3. يُرفع تلقائياً للسحابة (للأمان)
   ↓
4. تحصل على نسختين: محلية + سحابية
```

### 🕐 **المزامنة التلقائية:**
- **كل 30 دقيقة** يتم فحص الملفات الجديدة
- **رفع تلقائي** للملفات غير المرفوعة
- **إشعارات** عن حالة المزامنة

### 🔐 **الأمان:**
- **تشفير** الملفات قبل الرفع
- **أسماء مشفرة** للملفات
- **صلاحيات محدودة** للتطبيق

---

## 💻 **واجهة البرنامج الجديدة:**

### 🔗 **نافذة ربط السحابة:**
- **حالة الاتصال** (متصل/غير متصل)
- **معلومات المستخدم** (الاسم والبريد)
- **معلومات التخزين** (المساحة المستخدمة)
- **أزرار التحكم** (ربط/قطع/اختبار)

### 📊 **مراقبة المزامنة:**
- **شريط تقدم** المزامنة
- **عدد الملفات** المرفوعة
- **آخر مزامنة** ناجحة
- **حالة كل ملف** (متزامن/في انتظار/فشل)

---

## 🎉 **المميزات الجديدة:**

### ✨ **للمستخدم:**
- **أمان كامل** للمرفقات
- **وصول من أي جهاز** (عبر Google Drive)
- **نسخ احتياطي تلقائي** بدون تدخل
- **15 جيجابايت مجاني** من Google

### 🛠️ **للنظام:**
- **مزامنة ذكية** (رفع الجديد فقط)
- **إعادة محاولة** عند فشل الرفع
- **تنظيم تلقائي** للملفات في مجلدات
- **تتبع حالة** كل ملف

---

## 🔧 **استكشاف الأخطاء:**

### ❗ **"ملف الاعتمادات غير موجود"**
- **تأكد من وجود** `credentials.json` في مجلد البرنامج
- **تأكد من الاسم** بالضبط (بدون مسافات)

### ❗ **"فشل في تسجيل الدخول"**
- **تأكد من تفعيل** Google Drive API
- **تأكد من إعداد** OAuth consent screen
- **جرب إعادة تحميل** ملف credentials

### ❗ **"لا يوجد اتصال بالإنترنت"**
- **تحقق من الاتصال** بالإنترنت
- **تأكد من عدم حجب** Google Drive بواسطة Firewall

### ❗ **"تم رفض الصلاحيات"**
- **في المتصفح** اختر "السماح" لجميع الصلاحيات
- **تأكد من اختيار** الحساب الصحيح

---

## 📱 **كيفية الاستخدام:**

### 🔗 **الربط الأولي:**
1. **افتح البرنامج**
2. **اذهب إلى الإعدادات** → "التخزين السحابي"
3. **انقر "ربط Google Drive"**
4. **سيفتح متصفح** لتسجيل الدخول
5. **اختر حساب Google** وامنح الصلاحيات
6. **ستظهر رسالة نجاح** في البرنامج

### 📤 **الاستخدام اليومي:**
- **أضف مرفقات** كالمعتاد
- **النظام يرفع تلقائياً** كل 30 دقيقة
- **تابع الحالة** من نافذة التخزين السحابي
- **لا تحتاج فعل شيء!** 🎉

---

## 💰 **التكلفة:**

### 🆓 **مجاني تماماً:**
- **15 جيجابايت** مساحة مجانية
- **عدد لا محدود** من الملفات
- **مزامنة تلقائية** بدون حدود
- **أمان متقدم** مجاني

### 💵 **إذا احتجت أكثر:**
- **100 جيجابايت**: $1.99/شهر
- **200 جيجابايت**: $2.99/شهر
- **2 تيرابايت**: $9.99/شهر

---

## 🎯 **الخلاصة:**

### ✅ **ما ستحصل عليه:**
1. **أمان كامل** للمرفقات
2. **نسخ احتياطي تلقائي** 
3. **وصول من أي مكان**
4. **واجهة سهلة** ومتطورة
5. **مجاني 100%** حتى 15 جيجابايت

### 🚀 **البدء:**
1. **اتبع الخطوات أعلاه** (10 دقائق)
2. **ضع ملف credentials.json** في مجلد البرنامج
3. **افتح البرنامج** واربط Google Drive
4. **استمتع بالأمان الكامل!** 🎉

---

**هل تحتاج مساعدة؟** اتصل بي وسأساعدك خطوة بخطوة! 📞
