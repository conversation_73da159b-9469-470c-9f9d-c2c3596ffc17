using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using Microsoft.Win32;

namespace HR_InvoiceArchiver.Controls
{
    public partial class OfferFormControl : UserControl
    {
        private readonly OfferService _offerService;
        private string? _attachmentPath = null;

        public OfferFormControl(OfferService offerService)
        {
            InitializeComponent();
            _offerService = offerService;
            LoadScientificNames();
        }

        private void LoadScientificNames()
        {
            var names = _offerService.GetScientificNames().Select(s => s.Name).ToList();
            ScientificNameComboBox.ItemsSource = names;
        }

        private void AttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Filter = "All Files|*.pdf;*.jpg;*.jpeg;*.png;*.xlsx;*.xls"
            };
            if (dialog.ShowDialog() == true)
            {
                _attachmentPath = dialog.FileName;
            }
        }

        private void AddOfferButton_Click(object sender, RoutedEventArgs e)
        {
            var offer = new Offer
            {
                ScientificOffice = ScientificOfficeTextBox.Text,
                RepresentativeName = RepresentativeNameTextBox.Text,
                RepresentativePhone = RepresentativePhoneTextBox.Text,
                ScientificName = ScientificNameComboBox.Text,
                TradeName = TradeNameTextBox.Text,
                Price = decimal.TryParse(PriceTextBox.Text, out var price) ? price : 0,
                BonusOrDiscount = BonusOrDiscountTextBox.Text,
                Notes = NotesTextBox.Text,
                AttachmentPath = _attachmentPath,
                CreatedAt = DateTime.Now
            };
            _offerService.AddOffer(offer);
            MessageBox.Show("تمت إضافة العرض بنجاح!", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            ClearFields();
            LoadScientificNames();
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e) => ClearFields();

        private void ClearFields()
        {
            ScientificOfficeTextBox.Text = "";
            RepresentativeNameTextBox.Text = "";
            RepresentativePhoneTextBox.Text = "";
            ScientificNameComboBox.Text = "";
            TradeNameTextBox.Text = "";
            PriceTextBox.Text = "";
            BonusOrDiscountTextBox.Text = "";
            NotesTextBox.Text = "";
            _attachmentPath = null;
        }
    }
} 