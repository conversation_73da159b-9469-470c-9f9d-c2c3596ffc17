using System;
using System.Collections.Concurrent;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة المهام الخلفية لتحسين الأداء
    /// </summary>
    public interface IBackgroundTaskService
    {
        Task QueueBackgroundWorkItemAsync(Func<CancellationToken, Task> workItem);
        Task QueueBackgroundWorkItemAsync<T>(Func<T, CancellationToken, Task> workItem, T parameter);
        void StartPeriodicTask(string taskName, Func<CancellationToken, Task> task, TimeSpan interval);
        void StopPeriodicTask(string taskName);
        int GetQueueCount();
    }

    public class BackgroundTaskService : BackgroundService, IBackgroundTaskService
    {
        private readonly ConcurrentQueue<Func<CancellationToken, Task>> _workItems = new();
        private readonly SemaphoreSlim _signal = new(0);
        private readonly ConcurrentDictionary<string, CancellationTokenSource> _periodicTasks = new();
        private readonly ILogger<BackgroundTaskService> _logger;

        public BackgroundTaskService(ILogger<BackgroundTaskService> logger)
        {
            _logger = logger;
        }

        public async Task QueueBackgroundWorkItemAsync(Func<CancellationToken, Task> workItem)
        {
            if (workItem == null)
                throw new ArgumentNullException(nameof(workItem));

            _workItems.Enqueue(workItem);
            _signal.Release();

            await Task.CompletedTask;
        }

        public async Task QueueBackgroundWorkItemAsync<T>(Func<T, CancellationToken, Task> workItem, T parameter)
        {
            if (workItem == null)
                throw new ArgumentNullException(nameof(workItem));

            _workItems.Enqueue(cancellationToken => workItem(parameter, cancellationToken));
            _signal.Release();

            await Task.CompletedTask;
        }

        public void StartPeriodicTask(string taskName, Func<CancellationToken, Task> task, TimeSpan interval)
        {
            if (string.IsNullOrEmpty(taskName))
                throw new ArgumentException("Task name cannot be null or empty", nameof(taskName));

            if (task == null)
                throw new ArgumentNullException(nameof(task));

            // إيقاف المهمة إذا كانت موجودة
            StopPeriodicTask(taskName);

            var cancellationTokenSource = new CancellationTokenSource();
            _periodicTasks[taskName] = cancellationTokenSource;

            // بدء المهمة الدورية
            _ = Task.Run(async () =>
            {
                try
                {
                    while (!cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        try
                        {
                            await task(cancellationTokenSource.Token);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error in periodic task {TaskName}", taskName);
                        }

                        try
                        {
                            await Task.Delay(interval, cancellationTokenSource.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            break; // المهمة تم إلغاؤها
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Fatal error in periodic task {TaskName}", taskName);
                }
                finally
                {
                    _periodicTasks.TryRemove(taskName, out _);
                }
            }, cancellationTokenSource.Token);
        }

        public void StopPeriodicTask(string taskName)
        {
            if (_periodicTasks.TryRemove(taskName, out var cancellationTokenSource))
            {
                cancellationTokenSource.Cancel();
                cancellationTokenSource.Dispose();
            }
        }

        public int GetQueueCount()
        {
            return _workItems.Count;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Background Task Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await _signal.WaitAsync(stoppingToken);

                    if (_workItems.TryDequeue(out var workItem))
                    {
                        try
                        {
                            await workItem(stoppingToken);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error occurred executing background work item");
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in background task service");
                }
            }

            _logger.LogInformation("Background Task Service stopped");
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Background Task Service is stopping");

            // إيقاف جميع المهام الدورية
            foreach (var taskName in _periodicTasks.Keys.ToArray())
            {
                StopPeriodicTask(taskName);
            }

            await base.StopAsync(cancellationToken);
        }

        public override void Dispose()
        {
            _signal?.Dispose();

            // إيقاف وتنظيف جميع المهام الدورية
            foreach (var kvp in _periodicTasks)
            {
                kvp.Value.Cancel();
                kvp.Value.Dispose();
            }
            _periodicTasks.Clear();

            base.Dispose();
        }
    }

    /// <summary>
    /// مهام خلفية محددة للتطبيق
    /// </summary>
    public static class BackgroundTasks
    {
        /// <summary>
        /// تنظيف Cache منتهي الصلاحية
        /// </summary>
        public static async Task CleanupExpiredCacheAsync(ICacheService cacheService, CancellationToken cancellationToken)
        {
            try
            {
                cacheService.ClearExpiredItems();
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error cleaning up cache: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الإحصائيات في الخلفية
        /// </summary>
        public static async Task UpdateStatisticsAsync(IInvoiceService invoiceService, CancellationToken cancellationToken)
        {
            try
            {
                // تحديث الإحصائيات بطريقة محسنة
                await invoiceService.GetInvoiceStatisticsOptimizedAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف الملفات المؤقتة
        /// </summary>
        public static async Task CleanupTempFilesAsync(CancellationToken cancellationToken)
        {
            try
            {
                var tempPath = Path.GetTempPath();
                var appTempPath = Path.Combine(tempPath, "HR_InvoiceArchiver");
                
                if (Directory.Exists(appTempPath))
                {
                    var files = Directory.GetFiles(appTempPath, "*", SearchOption.AllDirectories);
                    var cutoffTime = DateTime.Now.AddHours(-24); // ملفات أقدم من 24 ساعة

                    foreach (var file in files)
                    {
                        try
                        {
                            var fileInfo = new FileInfo(file);
                            if (fileInfo.CreationTime < cutoffTime)
                            {
                                File.Delete(file);
                            }
                        }
                        catch
                        {
                            // تجاهل أخطاء حذف الملفات الفردية
                        }
                    }
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error cleaning up temp files: {ex.Message}");
            }
        }
    }
}
