using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Windows
{
    public partial class LoginWindow : Window
    {
        private readonly ISecurityService _securityService;
        private readonly ILoggingService _loggingService;
        private readonly ISettingsService _settingsService;
        private bool _isLoggingIn = false;

        public bool LoginSuccessful { get; private set; } = false;

        public LoginWindow()
        {
            InitializeComponent();
            
            // الحصول على الخدمات من DI Container
            var serviceProvider = App.ServiceProvider;
            _securityService = serviceProvider.GetRequiredService<ISecurityService>();
            _loggingService = serviceProvider.GetRequiredService<ILoggingService>();
            _settingsService = serviceProvider.GetRequiredService<ISettingsService>();

            // تعيين التركيز على حقل اسم المستخدم
            Loaded += (s, e) => UsernameTextBox.Focus();
            
            // معالجة مفتاح Escape للإغلاق
            KeyDown += (s, e) =>
            {
                if (e.Key == Key.Escape)
                    Close();
            };
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLoginAsync();
        }

        private async Task PerformLoginAsync()
        {
            if (_isLoggingIn)
                return;

            try
            {
                _isLoggingIn = true;
                SetUIState(false);
                ShowStatus("جاري تسجيل الدخول...", false);

                var username = UsernameTextBox.Text.Trim();
                var password = PasswordBox.Password;

                // التحقق من صحة البيانات المدخلة
                if (string.IsNullOrEmpty(username))
                {
                    ShowStatus("يرجى إدخال اسم المستخدم", true);
                    UsernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    ShowStatus("يرجى إدخال كلمة المرور", true);
                    PasswordBox.Focus();
                    return;
                }

                // محاولة تسجيل الدخول
                var result = await _securityService.LoginAsync(username, password);

                if (result.IsSuccessful)
                {
                    LoginSuccessful = true;
                    ShowStatus("تم تسجيل الدخول بنجاح", false);
                    
                    await _loggingService.LogInformationAsync($"تسجيل دخول ناجح للمستخدم: {username}");
                    
                    // إغلاق النافذة بعد تأخير قصير
                    await Task.Delay(1000);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    ShowStatus(result.ErrorMessage ?? "فشل في تسجيل الدخول", true);
                    
                    // مسح كلمة المرور
                    PasswordBox.Clear();
                    
                    // التركيز على الحقل المناسب
                    if (result.RemainingAttempts > 0)
                    {
                        PasswordBox.Focus();
                        ShowStatus($"{result.ErrorMessage} (المحاولات المتبقية: {result.RemainingAttempts})", true);
                    }
                    else if (result.LockoutDuration.HasValue)
                    {
                        ShowStatus($"تم قفل الحساب لمدة {result.LockoutDuration.Value.TotalMinutes:F0} دقيقة", true);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowStatus("حدث خطأ أثناء تسجيل الدخول", true);
                await _loggingService.LogErrorAsync("خطأ في نافذة تسجيل الدخول", ex);
            }
            finally
            {
                _isLoggingIn = false;
                SetUIState(true);
            }
        }

        private void SetUIState(bool enabled)
        {
            UsernameTextBox.IsEnabled = enabled;
            PasswordBox.IsEnabled = enabled;
            RememberMeCheckBox.IsEnabled = enabled;
            LoginButton.IsEnabled = enabled;
            ForgotPasswordButton.IsEnabled = enabled;
            
            LoadingProgressBar.Visibility = enabled ? Visibility.Collapsed : Visibility.Visible;
        }

        private void ShowStatus(string message, bool isError)
        {
            StatusTextBlock.Text = message;
            StatusBorder.Background = isError 
                ? (System.Windows.Media.Brush)FindResource("ValidationErrorBrush")
                : (System.Windows.Media.Brush)FindResource("PrimaryHueMidBrush");
            StatusBorder.Visibility = Visibility.Visible;

            // إخفاء الرسالة بعد 5 ثوان
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            timer.Tick += (s, e) =>
            {
                StatusBorder.Visibility = Visibility.Collapsed;
                timer.Stop();
            };
            timer.Start();
        }

        private async void ForgotPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var username = UsernameTextBox.Text.Trim();
                
                if (string.IsNullOrEmpty(username))
                {
                    ShowStatus("يرجى إدخال اسم المستخدم أولاً", true);
                    UsernameTextBox.Focus();
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد إعادة تعيين كلمة المرور للمستخدم '{username}'؟\n\nسيتم إنشاء كلمة مرور جديدة وعرضها لك.",
                    "إعادة تعيين كلمة المرور",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SetUIState(false);
                    ShowStatus("جاري إعادة تعيين كلمة المرور...", false);

                    var resetResult = await _securityService.ResetPasswordAsync(username, "default_answer");
                    
                    if (resetResult)
                    {
                        ShowStatus("تم إعادة تعيين كلمة المرور بنجاح", false);
                        MessageBox.Show(
                            "تم إعادة تعيين كلمة المرور بنجاح.\nيرجى التواصل مع مدير النظام للحصول على كلمة المرور الجديدة.",
                            "تم إعادة التعيين",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    else
                    {
                        ShowStatus("فشل في إعادة تعيين كلمة المرور", true);
                    }
                    
                    SetUIState(true);
                }
            }
            catch (Exception ex)
            {
                ShowStatus("حدث خطأ أثناء إعادة تعيين كلمة المرور", true);
                await _loggingService.LogErrorAsync("خطأ في إعادة تعيين كلمة المرور", ex);
                SetUIState(true);
            }
        }

        private void HelpButton_Click(object sender, RoutedEventArgs e)
        {
            var helpMessage = @"مساعدة تسجيل الدخول:

• اسم المستخدم الافتراضي: admin
• كلمة المرور الافتراضية: admin123

ميزات الأمان:
• يتم قفل الحساب بعد 3 محاولات فاشلة
• مدة القفل: 15 دقيقة
• انتهاء صلاحية الجلسة: 60 دقيقة

للمساعدة الإضافية، يرجى التواصل مع مدير النظام.";

            MessageBox.Show(helpMessage, "المساعدة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        protected override void OnClosed(EventArgs e)
        {
            // تنظيف الموارد
            if (!LoginSuccessful)
            {
                Application.Current.Shutdown();
            }
            
            base.OnClosed(e);
        }
    }
}
