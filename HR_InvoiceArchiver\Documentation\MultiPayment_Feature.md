# ميزة الدفع المتعدد (Multi-Payment Feature)

## نظرة عامة
ميزة الدفع المتعدد تسمح للمستخدمين بإنشاء وصل دفع واحد لعدة فواتير في نفس الوقت، مع نظام خصم ذكي يحسب الخصم تلقائياً بناءً على عدد الفواتير والمبلغ الإجمالي.

## المكونات الرئيسية

### 1. نماذج البيانات (Data Models)
- **MultiPaymentModel**: النموذج الرئيسي للدفع المتعدد
- **MultiPaymentInvoiceModel**: نموذج الفاتورة ضمن الدفع المتعدد
- **MultiPaymentEventArgs**: معاملات الأحداث للنموذج

### 2. واجهة المستخدم (UI Components)
- **MultiPaymentFormControl.xaml**: النموذج الرئيسي للدفع المتعدد
- **MultiPaymentFormControl.xaml.cs**: منطق العمل للنموذج
- أزرار الدفع المتعدد في PaymentsPage و DashboardPage

### 3. خدمات العمل (Business Services)
- **PaymentService.SaveMultiPaymentAsync()**: حفظ الدفع المتعدد
- منطق توزيع المدفوعات على الفواتير المختارة

## نظام الخصم الذكي

### قواعد حساب الخصم:
1. **بناءً على عدد الفواتير:**
   - 3-4 فواتير: خصم 1%
   - 5-9 فواتير: خصم 3%
   - 10+ فواتير: خصم 5%

2. **بناءً على المبلغ الإجمالي:**
   - 10,000,000 دينار فأكثر: خصم إضافي 3%

3. **الحد الأقصى للخصم:** 15%

### مثال على حساب الخصم:
```csharp
// 5 فواتير بمبلغ إجمالي 12,000,000 دينار
// خصم الفواتير: 3% (5 فواتير)
// خصم المبلغ: 3% (أكثر من 10 مليون)
// إجمالي الخصم: 6%
// مبلغ الخصم: 720,000 دينار
// المبلغ النهائي: 11,280,000 دينار
```

## كيفية الاستخدام

### 1. فتح نموذج الدفع المتعدد:
- من صفحة المدفوعات: اضغط على زر "إضافة وصل متعدد"
- من لوحة التحكم: اضغط على بطاقة "وصل متعدد"

### 2. اختيار المورد:
- اختر المورد من القائمة المنسدلة
- سيتم تحميل الفواتير غير المدفوعة للمورد المختار

### 3. اختيار الفواتير:
- حدد الفواتير المراد تسديدها باستخدام صناديق الاختيار
- سيتم حساب الخصم تلقائياً بناءً على العدد والمبلغ

### 4. مراجعة الملخص:
- عدد الفواتير المختارة
- إجمالي المبلغ
- نسبة الخصم المحسوبة
- مبلغ الخصم
- المبلغ النهائي بعد الخصم

### 5. إدخال تفاصيل الدفع:
- رقم الوصل
- تاريخ الدفع
- طريقة الدفع (نقدي/بطاقة بنكية)
- ملاحظات (اختياري)
- مرفق الوصل (اختياري)

### 6. حفظ الدفع:
- اضغط على زر "حفظ الوصل المتعدد"
- سيتم إنشاء وصولات دفع منفصلة لكل فاتورة
- تحديث حالة الفواتير تلقائياً

## التأثيرات البصرية

### انيميشن الفتح:
- تأثير تلاشي (Fade In)
- تأثير تكبير تدريجي (Scale Up)
- مدة الانيميشن: 400 مللي ثانية

### انيميشن الإغلاق:
- تأثير تلاشي (Fade Out)
- تأثير تصغير تدريجي (Scale Down)
- مدة الانيميشن: 300 مللي ثانية

## التحقق من صحة البيانات

### التحققات المطلوبة:
1. رقم الوصل غير فارغ
2. اختيار فاتورة واحدة على الأقل
3. تاريخ الدفع صحيح
4. طريقة الدفع محددة

### رسائل الخطأ:
- "يرجى إدخال رقم الوصل"
- "يرجى اختيار فاتورة واحدة على الأقل"
- "يرجى اختيار طريقة الدفع"

## إدارة الملفات المرفقة

### رفع المرفقات:
- دعم أنواع الملفات: PDF, JPG, PNG, DOCX
- الحد الأقصى لحجم الملف: 10 ميجابايت
- نسخ تلقائي للملفات إلى مجلد المشروع

### مسار حفظ المرفقات:
```
HR_InvoiceArchiver/Attachments/MultiPayments/YYYY/MM/
```

## قاعدة البيانات

### جداول متأثرة:
1. **Payments**: إدراج وصولات دفع منفصلة لكل فاتورة
2. **Invoices**: تحديث حالة الدفع والمبلغ المتبقي

### نمط أرقام الوصولات:
- الوصل الرئيسي: MP-001
- الوصولات الفرعية: MP-001-01, MP-001-02, MP-001-03...

## الاختبارات

### اختبارات الوحدة:
- اختبار حساب الخصم الذكي
- اختبار التحقق من صحة البيانات
- اختبار توزيع المدفوعات

### ملف الاختبارات:
```
HR_InvoiceArchiver/Tests/MultiPaymentTests.cs
```

## الأمان والأداء

### الأمان:
- التحقق من صحة البيانات قبل الحفظ
- حماية من SQL Injection
- التحقق من صلاحيات المستخدم

### الأداء:
- تحميل الفواتير بشكل غير متزامن
- استخدام Entity Framework للاستعلامات المحسنة
- ذاكرة التخزين المؤقت للموردين

## استكشاف الأخطاء

### الأخطاء الشائعة:
1. **"لا توجد فواتير للمورد المختار"**
   - التأكد من وجود فواتير غير مدفوعة للمورد

2. **"خطأ في حفظ الدفع المتعدد"**
   - التحقق من اتصال قاعدة البيانات
   - التأكد من صحة البيانات المدخلة

3. **"خطأ في رفع المرفق"**
   - التحقق من حجم الملف
   - التأكد من نوع الملف المدعوم

### السجلات (Logs):
- جميع العمليات مسجلة في ملف السجل
- مسار السجل: `HR_InvoiceArchiver/Logs/`

## التطوير المستقبلي

### ميزات مقترحة:
1. تصدير تقرير الدفع المتعدد إلى PDF
2. إرسال إشعارات بالبريد الإلكتروني
3. دعم العملات المتعددة
4. تكامل مع أنظمة المحاسبة الخارجية

### تحسينات الأداء:
1. تحسين استعلامات قاعدة البيانات
2. إضافة فهرسة للجداول
3. تحسين واجهة المستخدم للشاشات الكبيرة
